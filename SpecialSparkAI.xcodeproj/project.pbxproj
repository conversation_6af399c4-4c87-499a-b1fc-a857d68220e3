// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		7CDCD0B12DE18E7D00693D4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7CDCD0942DE18E7B00693D4C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7CDCD09B2DE18E7B00693D4C;
			remoteInfo = SpecialSparkAI;
		};
		7CDCD0BB2DE18E7D00693D4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7CDCD0942DE18E7B00693D4C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7CDCD09B2DE18E7B00693D4C;
			remoteInfo = SpecialSparkAI;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7CDCD09C2DE18E7B00693D4C /* SpecialSparkAI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SpecialSparkAI.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7CDCD0B02DE18E7D00693D4C /* SpecialSparkAITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SpecialSparkAITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7CDCD0BA2DE18E7D00693D4C /* SpecialSparkAIUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SpecialSparkAIUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7CDCD0C22DE18E7D00693D4C /* Exceptions for "SpecialSparkAI" folder in "SpecialSparkAI" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7CDCD09B2DE18E7B00693D4C /* SpecialSparkAI */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7CDCD09E2DE18E7B00693D4C /* SpecialSparkAI */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7CDCD0C22DE18E7D00693D4C /* Exceptions for "SpecialSparkAI" folder in "SpecialSparkAI" target */,
			);
			path = SpecialSparkAI;
			sourceTree = "<group>";
		};
		7CDCD0B32DE18E7D00693D4C /* SpecialSparkAITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SpecialSparkAITests;
			sourceTree = "<group>";
		};
		7CDCD0BD2DE18E7D00693D4C /* SpecialSparkAIUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SpecialSparkAIUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7CDCD0992DE18E7B00693D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD0AD2DE18E7D00693D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD0B72DE18E7D00693D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7CDCD0932DE18E7B00693D4C = {
			isa = PBXGroup;
			children = (
				7CDCD09E2DE18E7B00693D4C /* SpecialSparkAI */,
				7CDCD0B32DE18E7D00693D4C /* SpecialSparkAITests */,
				7CDCD0BD2DE18E7D00693D4C /* SpecialSparkAIUITests */,
				7CDCD09D2DE18E7B00693D4C /* Products */,
			);
			sourceTree = "<group>";
		};
		7CDCD09D2DE18E7B00693D4C /* Products */ = {
			isa = PBXGroup;
			children = (
				7CDCD09C2DE18E7B00693D4C /* SpecialSparkAI.app */,
				7CDCD0B02DE18E7D00693D4C /* SpecialSparkAITests.xctest */,
				7CDCD0BA2DE18E7D00693D4C /* SpecialSparkAIUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7CDCD09B2DE18E7B00693D4C /* SpecialSparkAI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDCD0C32DE18E7D00693D4C /* Build configuration list for PBXNativeTarget "SpecialSparkAI" */;
			buildPhases = (
				7CDCD0982DE18E7B00693D4C /* Sources */,
				7CDCD0992DE18E7B00693D4C /* Frameworks */,
				7CDCD09A2DE18E7B00693D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7CDCD09E2DE18E7B00693D4C /* SpecialSparkAI */,
			);
			name = SpecialSparkAI;
			packageProductDependencies = (
			);
			productName = SpecialSparkAI;
			productReference = 7CDCD09C2DE18E7B00693D4C /* SpecialSparkAI.app */;
			productType = "com.apple.product-type.application";
		};
		7CDCD0AF2DE18E7D00693D4C /* SpecialSparkAITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDCD0C82DE18E7D00693D4C /* Build configuration list for PBXNativeTarget "SpecialSparkAITests" */;
			buildPhases = (
				7CDCD0AC2DE18E7D00693D4C /* Sources */,
				7CDCD0AD2DE18E7D00693D4C /* Frameworks */,
				7CDCD0AE2DE18E7D00693D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7CDCD0B22DE18E7D00693D4C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7CDCD0B32DE18E7D00693D4C /* SpecialSparkAITests */,
			);
			name = SpecialSparkAITests;
			packageProductDependencies = (
			);
			productName = SpecialSparkAITests;
			productReference = 7CDCD0B02DE18E7D00693D4C /* SpecialSparkAITests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7CDCD0B92DE18E7D00693D4C /* SpecialSparkAIUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDCD0CB2DE18E7D00693D4C /* Build configuration list for PBXNativeTarget "SpecialSparkAIUITests" */;
			buildPhases = (
				7CDCD0B62DE18E7D00693D4C /* Sources */,
				7CDCD0B72DE18E7D00693D4C /* Frameworks */,
				7CDCD0B82DE18E7D00693D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7CDCD0BC2DE18E7D00693D4C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7CDCD0BD2DE18E7D00693D4C /* SpecialSparkAIUITests */,
			);
			name = SpecialSparkAIUITests;
			packageProductDependencies = (
			);
			productName = SpecialSparkAIUITests;
			productReference = 7CDCD0BA2DE18E7D00693D4C /* SpecialSparkAIUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7CDCD0942DE18E7B00693D4C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					7CDCD09B2DE18E7B00693D4C = {
						CreatedOnToolsVersion = 16.2;
					};
					7CDCD0AF2DE18E7D00693D4C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7CDCD09B2DE18E7B00693D4C;
					};
					7CDCD0B92DE18E7D00693D4C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7CDCD09B2DE18E7B00693D4C;
					};
				};
			};
			buildConfigurationList = 7CDCD0972DE18E7B00693D4C /* Build configuration list for PBXProject "SpecialSparkAI" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7CDCD0932DE18E7B00693D4C;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 7CDCD09D2DE18E7B00693D4C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7CDCD09B2DE18E7B00693D4C /* SpecialSparkAI */,
				7CDCD0AF2DE18E7D00693D4C /* SpecialSparkAITests */,
				7CDCD0B92DE18E7D00693D4C /* SpecialSparkAIUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7CDCD09A2DE18E7B00693D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD0AE2DE18E7D00693D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD0B82DE18E7D00693D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7CDCD0982DE18E7B00693D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD0AC2DE18E7D00693D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD0B62DE18E7D00693D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7CDCD0B22DE18E7D00693D4C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7CDCD09B2DE18E7B00693D4C /* SpecialSparkAI */;
			targetProxy = 7CDCD0B12DE18E7D00693D4C /* PBXContainerItemProxy */;
		};
		7CDCD0BC2DE18E7D00693D4C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7CDCD09B2DE18E7B00693D4C /* SpecialSparkAI */;
			targetProxy = 7CDCD0BB2DE18E7D00693D4C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7CDCD0C42DE18E7D00693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SpecialSparkAI/SpecialSparkAI.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SpecialSparkAI/Preview Content\"";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SpecialSparkAI/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.SpecialSparkAI;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7CDCD0C52DE18E7D00693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SpecialSparkAI/SpecialSparkAI.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SpecialSparkAI/Preview Content\"";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SpecialSparkAI/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.SpecialSparkAI;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7CDCD0C62DE18E7D00693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7CDCD0C72DE18E7D00693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7CDCD0C92DE18E7D00693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.SpecialSparkAITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SpecialSparkAI.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SpecialSparkAI";
			};
			name = Debug;
		};
		7CDCD0CA2DE18E7D00693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.SpecialSparkAITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SpecialSparkAI.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SpecialSparkAI";
			};
			name = Release;
		};
		7CDCD0CC2DE18E7D00693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.SpecialSparkAIUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SpecialSparkAI;
			};
			name = Debug;
		};
		7CDCD0CD2DE18E7D00693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.SpecialSparkAIUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SpecialSparkAI;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7CDCD0972DE18E7B00693D4C /* Build configuration list for PBXProject "SpecialSparkAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD0C62DE18E7D00693D4C /* Debug */,
				7CDCD0C72DE18E7D00693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDCD0C32DE18E7D00693D4C /* Build configuration list for PBXNativeTarget "SpecialSparkAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD0C42DE18E7D00693D4C /* Debug */,
				7CDCD0C52DE18E7D00693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDCD0C82DE18E7D00693D4C /* Build configuration list for PBXNativeTarget "SpecialSparkAITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD0C92DE18E7D00693D4C /* Debug */,
				7CDCD0CA2DE18E7D00693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDCD0CB2DE18E7D00693D4C /* Build configuration list for PBXNativeTarget "SpecialSparkAIUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD0CC2DE18E7D00693D4C /* Debug */,
				7CDCD0CD2DE18E7D00693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7CDCD0942DE18E7B00693D4C /* Project object */;
}
