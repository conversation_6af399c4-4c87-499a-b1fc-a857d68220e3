# SpecialSparkAI - API Integration Guide

## Overview
This document outlines the backend API integration for SpecialSparkAI, including Supabase database operations, Gemini AI integration, and agentic framework implementation.

## Supabase Integration

### Database Schema

#### Students Table
```sql
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    grade_level TEXT NOT NULL CHECK (grade_level IN ('K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12')),
    special_needs JSONB DEFAULT '[]',
    sensory_settings JSONB DEFAULT '{}',
    parent_email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### AI Teachers Table
```sql
CREATE TABLE ai_teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    personality JSONB NOT NULL,
    grade_specialization TEXT[] NOT NULL,
    avatar_url TEXT,
    system_prompt TEXT NOT NULL,
    model_config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Subjects Table
```sql
CREATE TABLE subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    grade_levels TEXT[] NOT NULL,
    description TEXT,
    icon_name TEXT,
    color_hex TEXT DEFAULT '#007AFF',
    learning_objectives JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Learning Sessions Table
```sql
CREATE TABLE learning_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    teacher_id UUID REFERENCES ai_teachers(id),
    subject_id UUID REFERENCES subjects(id),
    session_type TEXT NOT NULL DEFAULT 'lesson',
    conversation_history JSONB DEFAULT '[]',
    learning_metrics JSONB DEFAULT '{}',
    duration_minutes INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);
```

#### Achievements Table
```sql
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    badge_icon TEXT NOT NULL,
    category TEXT DEFAULT 'general',
    points INTEGER DEFAULT 0,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Swift Service Implementation

#### SupabaseService.swift
```swift
import Supabase
import Foundation

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()
    
    private let client: SupabaseClient
    
    private init() {
        client = SupabaseClient(
            supabaseURL: URL(string: Config.supabaseURL)!,
            supabaseKey: Config.supabaseAnonKey
        )
    }
    
    // MARK: - Student Operations
    func createStudent(_ student: Student) async throws -> Student {
        let response: Student = try await client
            .from("students")
            .insert(student)
            .select()
            .single()
            .execute()
            .value
        return response
    }
    
    func getStudent(id: UUID) async throws -> Student {
        let response: Student = try await client
            .from("students")
            .select()
            .eq("id", value: id)
            .single()
            .execute()
            .value
        return response
    }
    
    func updateStudentSensorySettings(studentId: UUID, settings: SensorySettings) async throws {
        try await client
            .from("students")
            .update(["sensory_settings": settings])
            .eq("id", value: studentId)
            .execute()
    }
    
    // MARK: - AI Teachers Operations
    func getAITeachers(forGrade grade: String) async throws -> [AITeacher] {
        let response: [AITeacher] = try await client
            .from("ai_teachers")
            .select()
            .contains("grade_specialization", value: [grade])
            .execute()
            .value
        return response
    }
    
    func getAITeacher(id: UUID) async throws -> AITeacher {
        let response: AITeacher = try await client
            .from("ai_teachers")
            .select()
            .eq("id", value: id)
            .single()
            .execute()
            .value
        return response
    }
    
    // MARK: - Subjects Operations
    func getSubjects(forGrade grade: String) async throws -> [Subject] {
        let response: [Subject] = try await client
            .from("subjects")
            .select()
            .contains("grade_levels", value: [grade])
            .execute()
            .value
        return response
    }
    
    // MARK: - Learning Sessions
    func createLearningSession(_ session: LearningSession) async throws -> LearningSession {
        let response: LearningSession = try await client
            .from("learning_sessions")
            .insert(session)
            .select()
            .single()
            .execute()
            .value
        return response
    }
    
    func updateLearningSession(id: UUID, conversationHistory: [ConversationMessage], metrics: LearningMetrics) async throws {
        try await client
            .from("learning_sessions")
            .update([
                "conversation_history": conversationHistory,
                "learning_metrics": metrics
            ])
            .eq("id", value: id)
            .execute()
    }
    
    // MARK: - Achievements
    func getAchievements(forStudent studentId: UUID) async throws -> [Achievement] {
        let response: [Achievement] = try await client
            .from("achievements")
            .select()
            .eq("student_id", value: studentId)
            .order("earned_at", ascending: false)
            .execute()
            .value
        return response
    }
    
    func awardAchievement(_ achievement: Achievement) async throws -> Achievement {
        let response: Achievement = try await client
            .from("achievements")
            .insert(achievement)
            .select()
            .single()
            .execute()
            .value
        return response
    }
}
```

## Gemini AI Integration

### GeminiService.swift
```swift
import Foundation

class GeminiService: ObservableObject {
    static let shared = GeminiService()
    
    private let apiKey = Config.geminiAPIKey
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta"
    
    private init() {}
    
    func generateResponse(
        for prompt: String,
        systemPrompt: String,
        conversationHistory: [ConversationMessage] = []
    ) async throws -> String {
        
        let url = URL(string: "\(baseURL)/models/\(Config.geminiModel):generateContent?key=\(apiKey)")!
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = GeminiRequest(
            contents: buildContents(systemPrompt: systemPrompt, prompt: prompt, history: conversationHistory),
            generationConfig: GeminiGenerationConfig(
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 1024
            ),
            safetySettings: [
                GeminiSafetySetting(category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
                GeminiSafetySetting(category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
                GeminiSafetySetting(category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
                GeminiSafetySetting(category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_MEDIUM_AND_ABOVE")
            ]
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw GeminiError.invalidResponse
        }
        
        let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
        
        guard let content = geminiResponse.candidates.first?.content.parts.first?.text else {
            throw GeminiError.noContent
        }
        
        return content
    }
    
    private func buildContents(
        systemPrompt: String,
        prompt: String,
        history: [ConversationMessage]
    ) -> [GeminiContent] {
        var contents: [GeminiContent] = []
        
        // Add system prompt
        contents.append(GeminiContent(
            role: "user",
            parts: [GeminiPart(text: "System: \(systemPrompt)")]
        ))
        
        // Add conversation history
        for message in history {
            contents.append(GeminiContent(
                role: message.isFromUser ? "user" : "model",
                parts: [GeminiPart(text: message.content)]
            ))
        }
        
        // Add current prompt
        contents.append(GeminiContent(
            role: "user",
            parts: [GeminiPart(text: prompt)]
        ))
        
        return contents
    }
}

// MARK: - Gemini Models
struct GeminiRequest: Codable {
    let contents: [GeminiContent]
    let generationConfig: GeminiGenerationConfig
    let safetySettings: [GeminiSafetySetting]
}

struct GeminiContent: Codable {
    let role: String
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String
}

struct GeminiGenerationConfig: Codable {
    let temperature: Double
    let topK: Int
    let topP: Double
    let maxOutputTokens: Int
}

struct GeminiSafetySetting: Codable {
    let category: String
    let threshold: String
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]
}

struct GeminiCandidate: Codable {
    let content: GeminiContent
}

enum GeminiError: Error {
    case invalidResponse
    case noContent
    case rateLimitExceeded
    case apiKeyInvalid
}
```

## Agentic Framework Integration

### LangGraph Integration
```swift
// AIAgentService.swift - LangGraph integration for complex conversation flows
class AIAgentService: ObservableObject {
    private let geminiService = GeminiService.shared
    private let supabaseService = SupabaseService.shared
    
    func createTeacherAgent(teacher: AITeacher, student: Student) -> TeacherAgent {
        return TeacherAgent(
            teacher: teacher,
            student: student,
            geminiService: geminiService,
            conversationGraph: buildConversationGraph(for: teacher)
        )
    }
    
    private func buildConversationGraph(for teacher: AITeacher) -> ConversationGraph {
        // LangGraph implementation for conversation flow
        // This would integrate with LangGraph framework
        return ConversationGraph(
            nodes: [
                .greeting,
                .assessmentCheck,
                .lessonDelivery,
                .comprehensionCheck,
                .adaptiveResponse,
                .conclusion
            ],
            edges: buildConversationEdges(),
            stateManager: ConversationStateManager()
        )
    }
}
```

### CrewAI Integration
```swift
// CrewAIService.swift - Multi-agent coordination
class CrewAIService: ObservableObject {
    func createTeacherCrew(for student: Student) -> TeacherCrew {
        return TeacherCrew(
            agents: [
                createMathTeacher(),
                createScienceTeacher(),
                createReadingTeacher(),
                createSpecialNeedsCoordinator()
            ],
            tasks: [
                .assessStudentNeeds,
                .createLearningPlan,
                .deliverInstruction,
                .trackProgress
            ],
            coordinator: CrewCoordinator()
        )
    }
}
```

## API Usage Examples

### Student Onboarding
```swift
// Create new student profile
let student = Student(
    firstName: "Alex",
    lastName: "Johnson",
    gradeLevel: .third,
    specialNeeds: [.autism, .adhd],
    sensorySettings: SensorySettings(
        reducedMotion: true,
        highContrast: false,
        textSize: .large
    )
)

let createdStudent = try await SupabaseService.shared.createStudent(student)
```

### AI Teacher Interaction
```swift
// Start learning session with AI teacher
let teacher = try await SupabaseService.shared.getAITeacher(id: teacherId)
let response = try await GeminiService.shared.generateResponse(
    for: "I need help with multiplication",
    systemPrompt: teacher.systemPrompt,
    conversationHistory: []
)
```

### Progress Tracking
```swift
// Award achievement
let achievement = Achievement(
    studentId: student.id,
    title: "Math Master",
    description: "Completed 10 multiplication problems",
    badgeIcon: "star.fill",
    category: "mathematics",
    points: 100
)

try await SupabaseService.shared.awardAchievement(achievement)
```

## Error Handling

### Common Error Types
- **Network Errors**: Connection timeouts, no internet
- **Authentication Errors**: Invalid API keys, expired tokens
- **Rate Limiting**: API quota exceeded
- **Data Validation**: Invalid input parameters
- **AI Errors**: Model unavailable, content filtering

### Error Handling Strategy
```swift
do {
    let result = try await apiCall()
    // Handle success
} catch let error as SupabaseError {
    // Handle Supabase-specific errors
} catch let error as GeminiError {
    // Handle Gemini-specific errors
} catch {
    // Handle general errors
}
```

## Performance Optimization

### Caching Strategy
- Cache AI teacher profiles locally
- Store conversation history in SwiftData
- Implement image caching for avatars
- Use background refresh for achievements

### Rate Limiting
- Implement request queuing for Gemini API
- Use exponential backoff for retries
- Monitor API usage and quotas
- Implement graceful degradation

## Security Considerations

### API Key Management
- Store API keys securely (not in code)
- Use environment-specific configurations
- Implement key rotation strategy
- Monitor for unauthorized usage

### Data Privacy
- Encrypt sensitive student data
- Implement COPPA compliance
- Secure parent communication
- Audit data access patterns
