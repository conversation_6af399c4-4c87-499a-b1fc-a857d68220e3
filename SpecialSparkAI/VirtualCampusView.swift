//
//  VirtualCampusView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct VirtualCampusView: View {
    let student: Student?
    @State private var selectedBuilding: VirtualBuilding?
    @State private var showingBuildingDetail = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Campus Header
                    campusHeaderView

                    // Campus Map/Overview
                    campusMapView

                    // Buildings Grid
                    buildingsGridView

                    // Outdoor Spaces
                    outdoorSpacesView

                    // Campus Activities
                    campusActivitiesView
                }
                .padding()
            }
            .navigationTitle("Virtual Campus")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingBuildingDetail) {
            if let building = selectedBuilding {
                BuildingDetailView(building: building)
            }
        }
    }

    private var campusHeaderView: some View {
        VStack(spacing: 15) {
            // Welcome message
            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text("Welcome to")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text("Your Learning Universe")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple, .pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Infinite worlds of knowledge await you")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Campus mascot or icon
                Image(systemName: "building.2.crop.circle.fill")
                    .font(.system(size: 60))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }

            // Learning universe stats
            HStack(spacing: 20) {
                CampusStatView(title: "Learning Realms", value: "∞", icon: "sparkles")
                CampusStatView(title: "AI Teachers", value: "∞", icon: "brain.head.profile")
                CampusStatView(title: "Active Learners", value: "150", icon: "person.3.fill")
                CampusStatView(title: "Knowledge Paths", value: "∞", icon: "arrow.triangle.branch")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 5)
        )
    }

    private var campusMapView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Learning Universe Navigator")
                .font(.headline)
                .fontWeight(.semibold)

            // Interactive universe map placeholder
            ZStack {
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [.purple.opacity(0.1), .blue.opacity(0.1), .pink.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(height: 200)

                VStack(spacing: 15) {
                    Image(systemName: "globe.americas.fill")
                        .font(.largeTitle)
                        .foregroundColor(.blue)

                    Text("Immersive Learning Dimensions")
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text("Explore infinite worlds • Learn through experience")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    Button("Enter the Universe") {
                        // Open detailed universe view
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
            }
        }
    }

    private var buildingsGridView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Campus Buildings")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                // Academic Building
                CampusAreaCard(
                    title: "Academic Center",
                    description: "Main classrooms and learning spaces",
                    icon: "building.2.fill",
                    color: .blue,
                    occupancy: 45,
                    capacity: 100
                ) {
                    // Navigate to academic building
                }

                // Library
                CampusAreaCard(
                    title: "Digital Library",
                    description: "Books, research, and quiet study",
                    icon: "books.vertical.fill",
                    color: .purple,
                    occupancy: 12,
                    capacity: 50
                ) {
                    // Navigate to library
                }

                // Science Labs
                CampusAreaCard(
                    title: "Science Labs",
                    description: "Experiments and discovery",
                    icon: "flask.fill",
                    color: .green,
                    occupancy: 8,
                    capacity: 30
                ) {
                    // Navigate to science labs
                }

                // Art Center
                CampusAreaCard(
                    title: "Creative Arts Center",
                    description: "Art, music, and creativity",
                    icon: "paintbrush.fill",
                    color: .pink,
                    occupancy: 15,
                    capacity: 40
                ) {
                    // Navigate to art center
                }

                // Gymnasium
                CampusAreaCard(
                    title: "Virtual Gymnasium",
                    description: "Sports and physical activities",
                    icon: "figure.run",
                    color: .orange,
                    occupancy: 20,
                    capacity: 60
                ) {
                    // Navigate to gymnasium
                }

                // Special Education Center
                CampusAreaCard(
                    title: "Special Support Center",
                    description: "Specialized learning support",
                    icon: "heart.circle.fill",
                    color: .mint,
                    occupancy: 5,
                    capacity: 20
                ) {
                    // Navigate to special education center
                }

                // Counseling Center
                CampusAreaCard(
                    title: "Wellness Center",
                    description: "Counseling and emotional support",
                    icon: "leaf.circle.fill",
                    color: .teal,
                    occupancy: 3,
                    capacity: 15
                ) {
                    // Navigate to counseling center
                }

                // Administration
                CampusAreaCard(
                    title: "Principal's Office",
                    description: "School administration",
                    icon: "person.crop.circle.badge.checkmark",
                    color: .indigo,
                    occupancy: 2,
                    capacity: 10
                ) {
                    // Navigate to administration
                }
            }
        }
    }

    private var outdoorSpacesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Outdoor Spaces")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    OutdoorSpaceCard(
                        title: "Virtual Playground",
                        description: "Fun games and activities",
                        icon: "figure.play",
                        color: .yellow,
                        weather: "Sunny"
                    )

                    OutdoorSpaceCard(
                        title: "Learning Garden",
                        description: "Nature studies and exploration",
                        icon: "leaf.fill",
                        color: .green,
                        weather: "Perfect"
                    )

                    OutdoorSpaceCard(
                        title: "Sports Field",
                        description: "Team sports and competitions",
                        icon: "sportscourt.fill",
                        color: .orange,
                        weather: "Cloudy"
                    )

                    OutdoorSpaceCard(
                        title: "Quiet Courtyard",
                        description: "Peaceful reflection space",
                        icon: "tree.fill",
                        color: .mint,
                        weather: "Breezy"
                    )
                }
                .padding(.horizontal)
            }
        }
    }

    private var campusActivitiesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Happening Now")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 10) {
                ActivityCard(
                    title: "Morning Assembly",
                    location: "Main Auditorium",
                    time: "9:00 AM",
                    participants: 85,
                    icon: "person.3.fill",
                    color: .blue
                )

                ActivityCard(
                    title: "Science Fair Setup",
                    location: "Science Labs",
                    time: "10:30 AM",
                    participants: 12,
                    icon: "flask.fill",
                    color: .green
                )

                ActivityCard(
                    title: "Art Exhibition",
                    location: "Creative Arts Center",
                    time: "2:00 PM",
                    participants: 25,
                    icon: "paintbrush.fill",
                    color: .pink
                )
            }
        }
    }
}

struct CampusStatView: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct OutdoorSpaceCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let weather: String

    var body: some View {
        VStack(spacing: 15) {
            // Weather indicator
            HStack {
                Spacer()
                HStack(spacing: 5) {
                    Image(systemName: weatherIcon(for: weather))
                        .font(.caption)
                        .foregroundColor(weatherColor(for: weather))
                    Text(weather)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }

            // Icon
            Image(systemName: icon)
                .font(.largeTitle)
                .foregroundColor(color)

            // Content
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }

            // Action button
            Button("Visit") {
                // Navigate to outdoor space
            }
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 8)
            .background(color)
            .cornerRadius(15)
        }
        .frame(width: 150)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 3)
        )
    }

    private func weatherIcon(for weather: String) -> String {
        switch weather.lowercased() {
        case "sunny":
            return "sun.max.fill"
        case "cloudy":
            return "cloud.fill"
        case "rainy":
            return "cloud.rain.fill"
        case "perfect":
            return "sparkles"
        case "breezy":
            return "wind"
        default:
            return "sun.max.fill"
        }
    }

    private func weatherColor(for weather: String) -> Color {
        switch weather.lowercased() {
        case "sunny":
            return .yellow
        case "cloudy":
            return .gray
        case "rainy":
            return .blue
        case "perfect":
            return .green
        case "breezy":
            return .mint
        default:
            return .yellow
        }
    }
}

struct ActivityCard: View {
    let title: String
    let location: String
    let time: String
    let participants: Int
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 15) {
            // Icon
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)

            // Content
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                HStack {
                    Label(location, systemImage: "location")
                    Spacer()
                    Label(time, systemImage: "clock")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }

            // Participants
            VStack {
                Text("\(participants)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("joined")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct BuildingDetailView: View {
    let building: VirtualBuilding

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Building Detail View")
                        .font(.title)

                    Text("Detailed view of \(building.name) would go here")
                        .font(.body)
                        .foregroundColor(.secondary)

                    // Add detailed building exploration interface
                }
                .padding()
            }
            .navigationTitle(building.name)
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

#Preview {
    VirtualCampusView(student: nil)
}
