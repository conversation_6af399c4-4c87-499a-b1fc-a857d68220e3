//
//  MainSchoolView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct MainSchoolView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @State private var selectedTab = 0
    @State private var showingProfile = false
    @State private var currentTime = Date()

    let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()

    var body: some View {
        TabView(selection: $selectedTab) {
            // Home/Dashboard
            SchoolDashboardView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(0)

            // Virtual Campus
            VirtualCampusView(student: nil) // TODO: Convert Student to StudentProfile
                .tabItem {
                    Image(systemName: "building.2.fill")
                    Text("Campus")
                }
                .tag(1)

            // Classrooms
            ClassroomsView(student: mockAuthService.currentStudent)
                .tabItem {
                    Image(systemName: "book.fill")
                    Text("Classes")
                }
                .tag(2)

            // AI Teachers
            SimpleAITeachersView()
                .tabItem {
                    Image(systemName: "person.3.fill")
                    Text("Teachers")
                }
                .tag(3)

            // Achievements & Progress
            AchievementsView(student: mockAuthService.currentStudent)
                .tabItem {
                    Image(systemName: "star.fill")
                    Text("Progress")
                }
                .tag(4)

            // Settings
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(5)
        }
        .accentColor(.blue)
        .onReceive(timer) { _ in
            currentTime = Date()
        }
    }
}

struct SchoolDashboardView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @State private var currentTime = Date()
    @State private var greeting = "Good Morning"
    @State private var todaysSchedule: [ScheduleItem] = []
    @State private var recentAchievements: [GameAchievement] = []
    @State private var upcomingAssignments: [Assignment] = []

    let timer = Timer.publish(every: 60, on: .main, in: .common).autoconnect()

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Welcome Header
                    welcomeHeader

                    // Quick Stats
                    quickStatsSection

                    // Today's Schedule
                    todaysScheduleSection

                    // Recent Achievements
                    recentAchievementsSection

                    // Upcoming Assignments
                    upcomingAssignmentsSection

                    // Quick Actions
                    quickActionsSection
                }
                .padding()
            }
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .onReceive(timer) { _ in
                updateGreeting()
            }
            .onAppear {
                updateGreeting()
                loadDashboardData()
            }
        }
    }

    private var welcomeHeader: some View {
        VStack(spacing: 15) {
            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text(greeting)
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text(mockAuthService.currentStudent?.firstName ?? "Student")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Ready to learn something amazing today?")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Student Avatar
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Text(String(mockAuthService.currentStudent?.firstName.prefix(1) ?? "S"))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    )
            }

            // Current Time and Weather
            HStack {
                Label(currentTime.formatted(date: .abbreviated, time: .shortened), systemImage: "clock")
                Spacer()
                Label("Perfect Learning Weather", systemImage: "sun.max.fill")
                    .foregroundColor(.orange)
            }
            .font(.caption)
            .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 5)
        )
    }

    private var quickStatsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 15) {
            StatCard(
                title: "Lessons Today",
                value: "\(todaysSchedule.count)",
                icon: "book.fill",
                color: .blue
            )

            StatCard(
                title: "Achievements",
                value: "0", // TODO: Implement achievements
                icon: "star.fill",
                color: .yellow
            )

            StatCard(
                title: "Grade Level",
                value: mockAuthService.currentStudent?.gradeLevel.displayName ?? "N/A",
                icon: "graduationcap.fill",
                color: .green
            )
        }
    }

    private var todaysScheduleSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Today's Schedule")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    // Navigate to full schedule
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if todaysSchedule.isEmpty {
                VStack(spacing: 10) {
                    Image(systemName: "calendar")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)

                    Text("No classes scheduled for today")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("Enjoy your free day!")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(.ultraThinMaterial)
                )
            } else {
                LazyVStack(spacing: 10) {
                    ForEach(todaysSchedule.prefix(3)) { item in
                        ScheduleItemCard(item: item)
                    }
                }
            }
        }
    }

    private var recentAchievementsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    // Navigate to achievements
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if recentAchievements.isEmpty {
                VStack(spacing: 10) {
                    Image(systemName: "star.circle")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)

                    Text("Keep learning to earn achievements!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(.ultraThinMaterial)
                )
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 15) {
                        ForEach(recentAchievements.prefix(5)) { achievement in
                            AchievementCard(achievement: achievement)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }

    private var upcomingAssignmentsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Upcoming Assignments")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    // Navigate to assignments
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if upcomingAssignments.isEmpty {
                VStack(spacing: 10) {
                    Image(systemName: "checkmark.circle")
                        .font(.largeTitle)
                        .foregroundColor(.green)

                    Text("All caught up!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("No assignments due soon")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(.ultraThinMaterial)
                )
            } else {
                LazyVStack(spacing: 10) {
                    ForEach(upcomingAssignments.prefix(3)) { assignment in
                        AssignmentCard(assignment: assignment)
                    }
                }
            }
        }
    }

    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                QuickActionCard(
                    title: "Start Learning",
                    subtitle: "Jump into a lesson",
                    icon: "play.circle.fill",
                    color: .green
                ) {
                    // Navigate to lessons
                }

                QuickActionCard(
                    title: "Meet Teachers",
                    subtitle: "Chat with AI teachers",
                    icon: "person.2.circle.fill",
                    color: .blue
                ) {
                    // Navigate to teachers
                }

                QuickActionCard(
                    title: "Explore Campus",
                    subtitle: "Visit virtual spaces",
                    icon: "building.2.circle.fill",
                    color: .purple
                ) {
                    // Navigate to campus
                }

                QuickActionCard(
                    title: "Check Progress",
                    subtitle: "See your growth",
                    icon: "chart.line.uptrend.xyaxis.circle.fill",
                    color: .orange
                ) {
                    // Navigate to progress
                }
            }
        }
    }

    private func updateGreeting() {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            greeting = "Good Morning"
        case 12..<17:
            greeting = "Good Afternoon"
        case 17..<21:
            greeting = "Good Evening"
        default:
            greeting = "Good Night"
        }
        currentTime = Date()
    }

    private func loadDashboardData() {
        // Load today's schedule
        todaysSchedule = createSampleSchedule()

        // Load recent achievements
        recentAchievements = [] // TODO: Implement achievements system

        // Load upcoming assignments
        upcomingAssignments = createSampleAssignments()
    }

    private func createSampleSchedule() -> [ScheduleItem] {
        return [
            ScheduleItem(
                id: UUID(),
                title: "Mathematics with Ms. Sophia",
                subject: "Math",
                startTime: Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date(),
                duration: 45,
                type: .lesson
            ),
            ScheduleItem(
                id: UUID(),
                title: "Science Discovery Lab",
                subject: "Science",
                startTime: Calendar.current.date(byAdding: .hour, value: 3, to: Date()) ?? Date(),
                duration: 60,
                type: .activity
            ),
            ScheduleItem(
                id: UUID(),
                title: "Creative Writing Workshop",
                subject: "English",
                startTime: Calendar.current.date(byAdding: .hour, value: 5, to: Date()) ?? Date(),
                duration: 30,
                type: .workshop
            )
        ]
    }

    private func createSampleAssignments() -> [Assignment] {
        return [
            Assignment(
                id: UUID(),
                title: "Math Practice: Addition & Subtraction",
                subject: "Mathematics",
                dueDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
                priority: .medium,
                isCompleted: false
            ),
            Assignment(
                id: UUID(),
                title: "Science Journal: Weather Observations",
                subject: "Science",
                dueDate: Calendar.current.date(byAdding: .day, value: 5, to: Date()) ?? Date(),
                priority: .low,
                isCompleted: false
            )
        ]
    }
}

// Supporting Models and Views
struct ScheduleItem: Identifiable {
    let id: UUID
    let title: String
    let subject: String
    let startTime: Date
    let duration: Int // minutes
    let type: ScheduleItemType
}

enum ScheduleItemType {
    case lesson, activity, workshop, breakTime, assembly
}

struct Assignment: Identifiable {
    let id: UUID
    let title: String
    let subject: String
    let dueDate: Date
    let priority: AssignmentPriority
    let isCompleted: Bool
}

enum AssignmentPriority {
    case low, medium, high

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}

#Preview {
    MainSchoolView()
}
