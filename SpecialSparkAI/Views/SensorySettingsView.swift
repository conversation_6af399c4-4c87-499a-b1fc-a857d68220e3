//
//  SensorySettingsView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct SensorySettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @State private var showingPreview = false

    var body: some View {
        List {
            // Visual Settings
            visualSection

            // Audio Settings
            audioSection

            // Motion & Animation Settings
            motionSection

            // Touch & Interaction Settings
            touchSection

            // Focus & Attention Settings
            focusSection

            // Preview Section
            previewSection
        }
        .navigationTitle("Sensory Settings")
        .navigationBarTitleDisplayMode(.large)
        .sheet(isPresented: $showingPreview) {
            SensoryPreviewView()
        }
    }

    // MARK: - Visual Settings
    private var visualSection: some View {
        Section("Visual Settings") {
            // Color Contrast
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "circle.lefthalf.filled")
                        .foregroundColor(.blue)
                    Text("Color Contrast")
                        .font(.headline)
                }

                Picker("Contrast Level", selection: $settingsManager.contrastLevel) {
                    Text("Standard").tag(ContrastLevel.standard)
                    Text("High").tag(ContrastLevel.high)
                    Text("Maximum").tag(ContrastLevel.maximum)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.vertical, 5)

            // Color Filters
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "eyedropper.halffull")
                        .foregroundColor(.purple)
                    Text("Color Filters")
                        .font(.headline)
                }

                Toggle("Reduce Red-Green", isOn: $settingsManager.reduceRedGreen)
                Toggle("Reduce Blue-Yellow", isOn: $settingsManager.reduceBlueYellow)
                Toggle("Grayscale Mode", isOn: $settingsManager.grayscaleMode)
            }
            .padding(.vertical, 5)

            // Text Size
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "textformat.size")
                        .foregroundColor(.green)
                    Text("Text Size")
                        .font(.headline)
                }

                HStack {
                    Text("A")
                        .font(.caption)
                    Slider(value: $settingsManager.textSizeMultiplier, in: 0.8...2.0, step: 0.1)
                    Text("A")
                        .font(.title)
                }

                Text("Current: \(Int(settingsManager.textSizeMultiplier * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 5)

            // Brightness & Flashing
            Toggle("Reduce Bright Colors", isOn: $settingsManager.reduceBrightColors)
            Toggle("Disable Flashing Elements", isOn: $settingsManager.disableFlashing)
            Toggle("Dark Mode Preference", isOn: $settingsManager.preferDarkMode)
        }
    }

    // MARK: - Audio Settings
    private var audioSection: some View {
        Section("Audio Settings") {
            // Volume Controls
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "speaker.wave.2.fill")
                        .foregroundColor(.orange)
                    Text("Volume Levels")
                        .font(.headline)
                }

                VStack(spacing: 8) {
                    HStack {
                        Text("UI Sounds")
                        Spacer()
                        Slider(value: $settingsManager.uiSoundVolume, in: 0...1)
                            .frame(width: 120)
                        Text("\(Int(settingsManager.uiSoundVolume * 100))%")
                            .font(.caption)
                            .frame(width: 35)
                    }

                    HStack {
                        Text("Voice Volume")
                        Spacer()
                        Slider(value: $settingsManager.voiceVolume, in: 0...1)
                            .frame(width: 120)
                        Text("\(Int(settingsManager.voiceVolume * 100))%")
                            .font(.caption)
                            .frame(width: 35)
                    }

                    HStack {
                        Text("Background Music")
                        Spacer()
                        Slider(value: $settingsManager.backgroundMusicVolume, in: 0...1)
                            .frame(width: 120)
                        Text("\(Int(settingsManager.backgroundMusicVolume * 100))%")
                            .font(.caption)
                            .frame(width: 35)
                    }
                }
            }
            .padding(.vertical, 5)

            // Audio Preferences
            Toggle("Mute All Sounds", isOn: $settingsManager.muteAllSounds)
            Toggle("Reduce Sudden Sounds", isOn: $settingsManager.reduceSuddenSounds)
            Toggle("Audio Descriptions", isOn: $settingsManager.enableAudioDescriptions)

            // Voice Settings
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "person.wave.2.fill")
                        .foregroundColor(.mint)
                    Text("Voice Settings")
                        .font(.headline)
                }

                Picker("Voice Speed", selection: $settingsManager.voiceSpeed) {
                    Text("Slow").tag(VoiceSpeed.slow)
                    Text("Normal").tag(VoiceSpeed.moderate)
                    Text("Fast").tag(VoiceSpeed.fast)
                }
                .pickerStyle(SegmentedPickerStyle())

                Picker("Voice Type", selection: $settingsManager.voiceType) {
                    Text("Gentle").tag(VoiceType.gentle)
                    Text("Cheerful").tag(VoiceType.cheerful)
                    Text("Calm").tag(VoiceType.calm)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.vertical, 5)
        }
    }

    // MARK: - Motion Settings
    private var motionSection: some View {
        Section("Motion & Animation") {
            Toggle("Reduce Motion", isOn: $settingsManager.reduceMotion)
            Toggle("Disable Auto-Play", isOn: $settingsManager.disableAutoPlay)
            Toggle("Slower Transitions", isOn: $settingsManager.slowerTransitions)

            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "speedometer")
                        .foregroundColor(.red)
                    Text("Animation Speed")
                        .font(.headline)
                }

                HStack {
                    Text("Slow")
                        .font(.caption)
                    Slider(value: $settingsManager.animationSpeed, in: 0.5...2.0, step: 0.1)
                    Text("Fast")
                        .font(.caption)
                }

                Text("Current: \(String(format: "%.1f", settingsManager.animationSpeed))x")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 5)
        }
    }

    // MARK: - Touch Settings
    private var touchSection: some View {
        Section("Touch & Interaction") {
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "hand.tap.fill")
                        .foregroundColor(.blue)
                    Text("Touch Sensitivity")
                        .font(.headline)
                }

                Picker("Sensitivity", selection: $settingsManager.touchSensitivity) {
                    Text("Light Touch").tag(TouchSensitivity.light)
                    Text("Normal").tag(TouchSensitivity.normal)
                    Text("Firm Touch").tag(TouchSensitivity.firm)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.vertical, 5)

            Toggle("Haptic Feedback", isOn: $settingsManager.hapticFeedback)
            Toggle("Button Hold Delay", isOn: $settingsManager.buttonHoldDelay)
            Toggle("Prevent Accidental Taps", isOn: $settingsManager.preventAccidentalTaps)

            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "timer")
                        .foregroundColor(.purple)
                    Text("Response Time")
                        .font(.headline)
                }

                HStack {
                    Text("Fast")
                        .font(.caption)
                    Slider(value: $settingsManager.responseTime, in: 0.5...3.0, step: 0.1)
                    Text("Slow")
                        .font(.caption)
                }

                Text("Current: \(String(format: "%.1f", settingsManager.responseTime))s")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 5)
        }
    }

    // MARK: - Focus Settings
    private var focusSection: some View {
        Section("Focus & Attention") {
            Toggle("Minimize Distractions", isOn: $settingsManager.minimizeDistractions)
            Toggle("Focus Mode", isOn: $settingsManager.focusMode)
            Toggle("Simplified Interface", isOn: $settingsManager.simplifiedInterface)

            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "eye.circle.fill")
                        .foregroundColor(.teal)
                    Text("Visual Complexity")
                        .font(.headline)
                }

                Picker("Complexity", selection: $settingsManager.visualComplexity) {
                    Text("Minimal").tag(VisualComplexity.minimal)
                    Text("Simple").tag(VisualComplexity.simple)
                    Text("Standard").tag(VisualComplexity.standard)
                    Text("Rich").tag(VisualComplexity.rich)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.vertical, 5)

            Toggle("Break Reminders", isOn: $settingsManager.breakReminders)
            Toggle("Progress Indicators", isOn: $settingsManager.showProgressIndicators)
        }
    }

    // MARK: - Preview Section
    private var previewSection: some View {
        Section("Preview") {
            Button(action: { showingPreview = true }) {
                HStack {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.blue)
                    Text("Preview Settings")
                        .font(.headline)
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
            }
            .foregroundColor(.primary)

            Button("Reset to Defaults") {
                settingsManager.resetSensorySettings()
            }
            .foregroundColor(.red)
        }
    }
}

// MARK: - Preview View
struct SensoryPreviewView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var settingsManager = SettingsManager.shared

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Sample content with current settings applied
                    Text("Preview of Your Settings")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("This is how text will appear with your current settings.")
                        .font(.body)
                        .multilineTextAlignment(.center)

                    // Sample buttons
                    HStack(spacing: 15) {
                        Button("Sample Button") {
                            // Sample action
                        }
                        .buttonStyle(.borderedProminent)

                        Button("Another Button") {
                            // Sample action
                        }
                        .buttonStyle(.bordered)
                    }

                    // Sample animation placeholder
                    Circle()
                        .fill(.blue)
                        .frame(width: 50, height: 50)

                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Settings Preview")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
