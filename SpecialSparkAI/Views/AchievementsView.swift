//
//  AchievementsView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct AchievementsView: View {
    let student: Student?
    @State private var selectedCategory: AchievementCategory?
    @State private var showingProgressDetail = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 25) {
                    // Progress overview
                    progressOverviewView

                    // Achievement categories
                    achievementCategoriesView

                    // Recent achievements
                    recentAchievementsView

                    // Subject progress
                    subjectProgressView

                    // Learning streaks
                    learningStreaksView

                    // Goals and milestones
                    goalsAndMilestonesView
                }
                .padding()
            }
            .navigationTitle("Progress & Achievements")
            .navigationBarTitleDisplayMode(.large)
        }
    }

    private var progressOverviewView: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text("Your Learning Journey")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Keep up the amazing work!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Total points
                VStack {
                    Text("\(getTotalPoints())")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Total Points")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Progress stats
            HStack(spacing: 20) {
                ProgressStatCard(
                    title: "Achievements",
                    value: "\(student?.achievements.count ?? 0)",
                    icon: "star.fill",
                    color: .yellow
                )

                ProgressStatCard(
                    title: "Lessons Completed",
                    value: "47",
                    icon: "checkmark.circle.fill",
                    color: .green
                )

                ProgressStatCard(
                    title: "Learning Streak",
                    value: "12 days",
                    icon: "flame.fill",
                    color: .orange
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 5)
        )
    }

    private var achievementCategoriesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Achievement Categories")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                ForEach(AchievementCategory.allCases, id: \.self) { category in
                    AchievementCategoryCard(
                        category: category,
                        count: getAchievementCount(for: category),
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = selectedCategory == category ? nil : category
                    }
                }
            }
        }
    }

    private var recentAchievementsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    // Show all achievements
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            let filteredAchievements = getFilteredAchievements()

            if filteredAchievements.isEmpty {
                EmptyAchievementsView()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(filteredAchievements.prefix(5)) { achievement in
                        DetailedAchievementCard(achievement: achievement)
                    }
                }
            }
        }
    }

    private var subjectProgressView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Subject Progress")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                SubjectProgressCard(
                    subject: "Mathematics",
                    progress: 0.75,
                    level: "Grade 3",
                    color: .blue
                )

                SubjectProgressCard(
                    subject: "English",
                    progress: 0.60,
                    level: "Grade 3",
                    color: .purple
                )

                SubjectProgressCard(
                    subject: "Science",
                    progress: 0.85,
                    level: "Grade 3",
                    color: .green
                )

                SubjectProgressCard(
                    subject: "Art",
                    progress: 0.90,
                    level: "Grade 3",
                    color: .pink
                )
            }
        }
    }

    private var learningStreaksView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Learning Streaks")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                LearningStreakCard(
                    title: "Daily Learning",
                    currentStreak: 12,
                    bestStreak: 25,
                    icon: "calendar",
                    color: .orange
                )

                LearningStreakCard(
                    title: "Math Mastery",
                    currentStreak: 8,
                    bestStreak: 15,
                    icon: "function",
                    color: .blue
                )

                LearningStreakCard(
                    title: "Reading Adventure",
                    currentStreak: 5,
                    bestStreak: 20,
                    icon: "book",
                    color: .purple
                )
            }
        }
    }

    private var goalsAndMilestonesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Goals & Milestones")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                GoalCard(
                    title: "Complete 50 Math Lessons",
                    progress: 0.94,
                    current: 47,
                    target: 50,
                    color: .blue
                )

                GoalCard(
                    title: "Read 20 Books This Month",
                    progress: 0.65,
                    current: 13,
                    target: 20,
                    color: .purple
                )

                GoalCard(
                    title: "Earn 1000 Points",
                    progress: 0.82,
                    current: 820,
                    target: 1000,
                    color: .green
                )
            }
        }
    }

    // Helper methods
    private func getTotalPoints() -> Int {
        return student?.achievements.reduce(0) { $0 + $1.points } ?? 0
    }

    private func getAchievementCount(for category: AchievementCategory) -> Int {
        return student?.achievements.filter { $0.category == category }.count ?? 0
    }

    private func getFilteredAchievements() -> [GameAchievement] {
        guard let achievements = student?.achievements else { return [] }

        if let selectedCategory = selectedCategory {
            return achievements.filter { $0.category == selectedCategory }
        }

        return achievements.sorted {
            guard let date1 = $0.unlockedAt, let date2 = $1.unlockedAt else { return false }
            return date1 > date2
        }
    }
}

struct ProgressStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
}

struct AchievementCategoryCard: View {
    let category: AchievementCategory
    let count: Int
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 10) {
                Image(systemName: categoryIcon(for: category))
                    .font(.title2)
                    .foregroundColor(categoryColor(for: category))

                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)

                Text("\(count)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(isSelected ? AnyShapeStyle(categoryColor(for: category).opacity(0.2)) : AnyShapeStyle(.ultraThinMaterial))
                    .overlay(
                        RoundedRectangle(cornerRadius: 15)
                            .stroke(isSelected ? categoryColor(for: category) : .clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func categoryIcon(for category: AchievementCategory) -> String {
        switch category {
        case .learning:
            return "brain.head.profile"
        case .social:
            return "person.2.fill"
        case .creativity:
            return "paintbrush.fill"
        case .leadership:
            return "crown.fill"
        case .persistence:
            return "target"
        case .kindness:
            return "heart.fill"
        case .innovation:
            return "lightbulb.fill"
        case .exploration:
            return "safari.fill"
        }
    }

    private func categoryColor(for category: AchievementCategory) -> Color {
        switch category {
        case .learning:
            return .blue
        case .social:
            return .green
        case .creativity:
            return .purple
        case .leadership:
            return .yellow
        case .persistence:
            return .orange
        case .kindness:
            return .pink
        case .innovation:
            return .indigo
        case .exploration:
            return .teal
        }
    }
}

struct DetailedAchievementCard: View {
    let achievement: GameAchievement

    var body: some View {
        HStack(spacing: 15) {
            // Achievement badge
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: categoryColors(for: achievement.category),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)

                Image(systemName: categoryIcon(for: achievement.category))
                    .font(.title3)
                    .foregroundColor(.white)
            }

            // Achievement info
            VStack(alignment: .leading, spacing: 5) {
                Text(achievement.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(achievement.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Text(achievement.unlockedAt?.formatted(date: .abbreviated, time: .omitted) ?? "Not unlocked")
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    Spacer()

                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.yellow)

                        Text("\(achievement.points) pts")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                }
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }

    private func categoryIcon(for category: AchievementCategory) -> String {
        switch category {
        case .learning:
            return "brain.head.profile"
        case .social:
            return "person.2.fill"
        case .creativity:
            return "paintbrush.fill"
        case .leadership:
            return "crown.fill"
        case .persistence:
            return "target"
        case .kindness:
            return "heart.fill"
        case .innovation:
            return "lightbulb.fill"
        case .exploration:
            return "safari.fill"
        }
    }

    private func categoryColors(for category: AchievementCategory) -> [Color] {
        switch category {
        case .learning:
            return [.blue, .cyan]
        case .social:
            return [.green, .mint]
        case .creativity:
            return [.purple, .pink]
        case .leadership:
            return [.yellow, .orange]
        case .persistence:
            return [.orange, .red]
        case .kindness:
            return [.pink, .red]
        case .innovation:
            return [.indigo, .purple]
        case .exploration:
            return [.teal, .blue]
        }
    }
}

struct LearningStreakCard: View {
    let title: String
    let currentStreak: Int
    let bestStreak: Int
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                HStack {
                    Text("Current: \(currentStreak)")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("•")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("Best: \(bestStreak)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // Streak visualization
            HStack(spacing: 3) {
                ForEach(0..<min(currentStreak, 7), id: \.self) { _ in
                    Circle()
                        .fill(color)
                        .frame(width: 6, height: 6)
                }

                ForEach(0..<max(0, 7 - currentStreak), id: \.self) { _ in
                    Circle()
                        .fill(color.opacity(0.3))
                        .frame(width: 6, height: 6)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
}

struct GoalCard: View {
    let title: String
    let progress: Double
    let current: Int
    let target: Int
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Text("\(current)/\(target)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(color)
            }

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 6)
                        .fill(color.opacity(0.2))
                        .frame(height: 8)

                    RoundedRectangle(cornerRadius: 6)
                        .fill(color)
                        .frame(width: geometry.size.width * progress, height: 8)
                        .animation(.easeInOut(duration: 1), value: progress)
                }
            }
            .frame(height: 8)

            HStack {
                Text("\(Int(progress * 100))% Complete")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()

                if progress >= 1.0 {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)

                        Text("Goal Achieved!")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                } else {
                    Text("\(target - current) to go")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
}

struct EmptyAchievementsView: View {
    var body: some View {
        VStack(spacing: 15) {
            Image(systemName: "star.circle")
                .font(.largeTitle)
                .foregroundColor(.secondary)

            Text("No Achievements Yet")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Complete lessons and activities to earn your first achievement!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Start Learning") {
                // Navigate to lessons
            }
            .font(.subheadline)
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(.blue)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
}

#Preview {
    AchievementsView(student: nil)
}
