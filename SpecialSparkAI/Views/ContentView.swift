//
//  ContentView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @StateObject private var mockAuthService = MockAuthService.shared

    var body: some View {
        Group {
            if !mockAuthService.isAuthenticated {
                StudentProfileView()
            } else {
                TabView {
                    // Main School View
                    MainSchoolView()
                        .tabItem {
                            Image(systemName: "building.2.fill")
                            Text("School")
                        }

                    // AI Teachers
                    AITeachersView()
                        .tabItem {
                            Image(systemName: "brain.head.profile")
                            Text("AI Teachers")
                        }

                    // Virtual Campus
                    VirtualCampusView()
                        .tabItem {
                            Image(systemName: "map.fill")
                            Text("Campus")
                        }

                    // Classrooms
                    ClassroomsView()
                        .tabItem {
                            Image(systemName: "door.left.hand.open")
                            Text("Classrooms")
                        }

                    // Achievements
                    AchievementsView()
                        .tabItem {
                            Image(systemName: "trophy.fill")
                            Text("Achievements")
                        }

                    // Settings
                    SettingsView()
                        .tabItem {
                            Image(systemName: "gearshape.fill")
                            Text("Settings")
                        }

                    // AI Test
                    GeminiTestView()
                        .tabItem {
                            Image(systemName: "brain.head.profile")
                            Text("AI Test")
                        }

                    // Adaptive Learning Test
                    AdaptiveLearningTestView()
                        .tabItem {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                            Text("Learning AI")
                        }
                }
                .accentColor(.blue)
            }
        }
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Student.self, inMemory: true)
}
