//
//  ContentView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var students: [Student]
    @Query private var teachers: [AITeacher]
    @State private var selectedTab = 0
    @State private var currentStudent: Student?
    @State private var showingWelcome = true

    var body: some View {
        Group {
            if showingWelcome {
                WelcomeView(showingWelcome: $showingWelcome, currentStudent: $currentStudent)
            } else {
                MainSchoolView(currentStudent: currentStudent)
            }
        }
        .onAppear {
            setupInitialData()
        }
    }

    private func setupInitialData() {
        // Create sample data if none exists
        if students.isEmpty {
            createSampleStudent()
        }
        if teachers.isEmpty {
            createSampleTeachers()
        }
    }

    private func createSampleStudent() {
        let student = Student(
            firstName: "Alex",
            lastName: "Johnson",
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -8, to: Date()) ?? Date(),
            gradeLevel: 3
        )

        let learningProfile = LearningProfile(
            studentId: student.id,
            learningStyle: .visual,
            preferredPace: .moderate
        )
        learningProfile.interests = ["Science", "Art", "Animals"]
        learningProfile.strengths = ["Creative thinking", "Problem solving"]
        learningProfile.challenges = ["Reading comprehension", "Math facts"]

        student.learningProfile = learningProfile

        modelContext.insert(student)
        modelContext.insert(learningProfile)

        try? modelContext.save()
    }

    private func createSampleTeachers() {
        let mathTeacher = AITeacher(
            name: "Ms. Sophia Numbers",
            subject: .mathematics
        )
        mathTeacher.personality = TeacherPersonality(teacherId: mathTeacher.id)
        mathTeacher.specializations = ["Elementary Math", "Special Needs Support", "Visual Learning"]

        let englishTeacher = AITeacher(
            name: "Mr. Oliver Words",
            subject: .english
        )
        englishTeacher.personality = TeacherPersonality(teacherId: englishTeacher.id)
        englishTeacher.specializations = ["Reading Comprehension", "Creative Writing", "Dyslexia Support"]

        let scienceTeacher = AITeacher(
            name: "Dr. Luna Discovery",
            subject: .science
        )
        scienceTeacher.personality = TeacherPersonality(teacherId: scienceTeacher.id)
        scienceTeacher.specializations = ["Hands-on Experiments", "Nature Studies", "STEM Integration"]

        modelContext.insert(mathTeacher)
        modelContext.insert(englishTeacher)
        modelContext.insert(scienceTeacher)

        try? modelContext.save()
    }
}

struct WelcomeView: View {
    @Binding var showingWelcome: Bool
    @Binding var currentStudent: Student?
    @Environment(\.modelContext) private var modelContext
    @Query private var students: [Student]

    var body: some View {
        ZStack {
            // Animated background
            LinearGradient(
                colors: [
                    Color.blue.opacity(0.3),
                    Color.purple.opacity(0.3),
                    Color.pink.opacity(0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: showingWelcome)

            VStack(spacing: 30) {
                // School Logo and Name
                VStack(spacing: 20) {
                    Image(systemName: "sparkles")
                        .font(.system(size: 80))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.orange, .yellow, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(showingWelcome ? 1.0 : 0.8)
                        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: showingWelcome)

                    Text("SpecialSpark AI")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Where Every Child Shines ✨")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                Spacer()

                // Student Selection
                VStack(spacing: 20) {
                    Text("Welcome to Your Virtual School!")
                        .font(.title)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)

                    Text("Select your profile to enter the magical world of learning")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    if !students.isEmpty {
                        LazyVGrid(columns: [GridItem(.adaptive(minimum: 150))], spacing: 20) {
                            ForEach(students) { student in
                                StudentProfileCard(student: student) {
                                    currentStudent = student
                                    withAnimation(.spring()) {
                                        showingWelcome = false
                                    }
                                }
                            }
                        }
                        .padding(.horizontal)
                    }

                    Button(action: {
                        // Create new student profile
                        createNewStudent()
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                            Text("Create New Student")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .background(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(15)
                    }
                }

                Spacer()

                // Footer
                VStack(spacing: 10) {
                    Text("Powered by Advanced AI Teachers")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    HStack(spacing: 20) {
                        Label("Inclusive", systemImage: "heart.fill")
                        Label("Adaptive", systemImage: "brain.head.profile")
                        Label("Engaging", systemImage: "star.fill")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
            }
            .padding()
        }
    }

    private func createNewStudent() {
        // For now, create a sample student. In a real app, this would open a registration form
        let newStudent = Student(
            firstName: "New",
            lastName: "Student",
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -7, to: Date()) ?? Date(),
            gradeLevel: 2
        )

        modelContext.insert(newStudent)
        try? modelContext.save()

        currentStudent = newStudent
        withAnimation(.spring()) {
            showingWelcome = false
        }
    }
}

struct StudentProfileCard: View {
    let student: Student
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                // Avatar
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Text(String(student.firstName.prefix(1)))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    )

                VStack(spacing: 5) {
                    Text(student.fullName)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text("Grade \(student.gradeLevel)")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    if !student.achievements.isEmpty {
                        HStack {
                            Image(systemName: "star.fill")
                                .foregroundColor(.yellow)
                            Text("\(student.achievements.count) achievements")
                                .font(.caption2)
                        }
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Student.self, inMemory: true)
}
