//
//  AITeachersView.swift
//  SpecialSparkAI
//
//  AI Agent Teachers with LangGraph and CrewAI Integration
//

import SwiftUI
import SwiftData

struct AITeachersView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @StateObject private var aiAgentService = AIAgentService()
    @State private var selectedAgent: AIAgent?
    @State private var showingAgentDetail = false
    @State private var searchText = ""
    @State private var selectedAgentType: AgentType?
    @State private var showingStudentProfile = false
    @State private var showingCrewBuilder = false
    @State private var gradeBasedTeachers: [AIAgent] = []
    @State private var selectedSubjectCategory: SubjectCategory?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Student Grade Header
                studentGradeHeader

                // Connection Status
                connectionStatusBar

                // Subject Category Filter
                subjectCategoryFilter

                // Search Bar
                searchBar

                // Main Content
                if !mockAuthService.isAuthenticated {
                    authenticationPrompt
                } else if gradeBasedTeachers.isEmpty {
                    loadingTeachersState
                } else {
                    teachersGridView
                }

                Spacer()

                // Action Buttons
                actionButtonsBar
            }
            .navigationTitle("Your AI Teachers")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Student Profile", systemImage: "person.circle") {
                            showingStudentProfile = true
                        }

                        Button("Build Study Crew", systemImage: "person.3.fill") {
                            showingCrewBuilder = true
                        }

                        Button("Learning Analytics", systemImage: "chart.bar.fill") {
                            // Show analytics
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title2)
                    }
                }
            }
            .onAppear {
                loadGradeBasedTeachers()
            }
            .onChange(of: mockAuthService.currentStudent) { _, _ in
                loadGradeBasedTeachers()
            }
        }
        .sheet(isPresented: $showingStudentProfile) {
            StudentProfileView()
        }
        .sheet(isPresented: $showingCrewBuilder) {
            CrewBuilderView(
                aiAgentService: aiAgentService,
                availableAgents: gradeBasedTeachers
            )
        }
        .sheet(item: $selectedAgent) { agent in
            AgentDetailView(agent: agent, aiAgentService: aiAgentService)
        }
    }

    // MARK: - UI Components

    private var studentGradeHeader: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    if let student = mockAuthService.currentStudent {
                        Text("Hello, \(student.firstName)!")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.blue, .purple, .pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )

                        Text("\(student.gradeLevel.displayName) • \(student.schoolLevel.displayName)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text("Your AI Teachers")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.blue, .purple, .pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )

                        Text("Sign in to see your grade-specific teachers")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Teacher Stats
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(gradeBasedTeachers.count)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("AI Teachers")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Special Needs Indicator
            if let student = mockAuthService.currentStudent,
               !student.specialNeeds.isEmpty && student.specialNeeds != [.none] {
                HStack {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.pink)

                    Text("Specialized support for: \(student.specialNeeds.map { $0.displayName }.joined(separator: ", "))")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .background(Color.pink.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }

    private var connectionStatusBar: some View {
        HStack {
            Circle()
                .fill(connectionStatusColor)
                .frame(width: 8, height: 8)

            Text(connectionStatusText)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            if case .connected = aiAgentService.connectionStatus {
                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.green)
                    Text("Gemini Flash 2.0")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }

    private var connectionStatusColor: Color {
        switch aiAgentService.connectionStatus {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected:
            return .red
        case .error:
            return .red
        }
    }

    private var connectionStatusText: String {
        switch aiAgentService.connectionStatus {
        case .connected:
            return "Connected to AI Services"
        case .connecting:
            return "Connecting to AI Services..."
        case .disconnected:
            return "Disconnected from AI Services"
        case .error(let message):
            return "Error: \(message)"
        }
    }

    private var subjectCategoryFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                SubjectCategoryFilterChip(
                    title: "All Subjects",
                    isSelected: selectedSubjectCategory == nil,
                    icon: "sparkles"
                ) {
                    selectedSubjectCategory = nil
                }

                ForEach(SubjectCategory.allCases, id: \.self) { category in
                    SubjectCategoryFilterChip(
                        title: category.displayName,
                        isSelected: selectedSubjectCategory == category,
                        icon: iconForSubjectCategory(category)
                    ) {
                        selectedSubjectCategory = category
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search AI agents...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button("Clear") {
                    searchText = ""
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    private var authenticationPrompt: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "person.circle")
                .font(.system(size: 80))
                .foregroundColor(.blue.opacity(0.3))

            VStack(spacing: 8) {
                Text("Welcome to SpecialSpark AI")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Sign in to see your personalized AI teachers based on your grade level and learning needs")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("Sign In") {
                // Navigate to auth view
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)

            Spacer()
        }
        .padding()
    }

    private var loadingTeachersState: some View {
        VStack(spacing: 20) {
            Spacer()

            ProgressView()
                .scaleEffect(1.5)

            VStack(spacing: 8) {
                Text("Loading Your AI Teachers")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Preparing personalized teachers for your grade level...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Spacer()
        }
        .padding()
    }

    private var teachersGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(filteredTeachers) { teacher in
                    AITeacherCard(teacher: teacher) {
                        selectedAgent = teacher
                        showingAgentDetail = true
                    }
                }
            }
            .padding()
        }
    }

    private var actionButtonsBar: some View {
        HStack(spacing: 16) {
            Button("Quick Chat") {
                // Start quick chat with available teacher
                if let teacher = gradeBasedTeachers.first {
                    selectedAgent = teacher
                    showingAgentDetail = true
                }
            }
            .buttonStyle(.bordered)
            .disabled(gradeBasedTeachers.isEmpty)

            Button("Study Crew") {
                showingCrewBuilder = true
            }
            .buttonStyle(.bordered)
            .disabled(gradeBasedTeachers.count < 2)

            Button("Profile") {
                showingStudentProfile = true
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(.ultraThinMaterial)
    }

    // MARK: - Helper Methods

    private func loadGradeBasedTeachers() {
        guard let student = mockAuthService.currentStudent else {
            gradeBasedTeachers = []
            return
        }

        // Load teachers for the student's grade level
        gradeBasedTeachers = GradeBasedTeacherService.shared.getTeachersFor(student: student)
    }

    private var filteredTeachers: [AIAgent] {
        var filtered = gradeBasedTeachers

        // Filter by agent type
        if let selectedType = selectedAgentType {
            filtered = filtered.filter { $0.agentType == selectedType }
        }

        // Filter by subject category
        if let selectedCategory = selectedSubjectCategory {
            let subjects = SubjectDataService.shared.allSubjects.filter { $0.category == selectedCategory }
            let subjectIds = Set(subjects.map { $0.id })
            filtered = filtered.filter { teacher in
                if let subjectId = teacher.subjectId {
                    return subjectIds.contains(subjectId)
                }
                return selectedCategory == .specialNeeds && teacher.agentType != .subjectSpecialist
            }
        }

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { teacher in
                teacher.name.localizedCaseInsensitiveContains(searchText) ||
                teacher.specialization.localizedCaseInsensitiveContains(searchText) ||
                teacher.agentType.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }

        return filtered
    }

    private func iconForAgentType(_ agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist:
            return "graduationcap.fill"
        case .learningCoach:
            return "person.crop.circle.badge.checkmark"
        case .emotionalSupport:
            return "heart.fill"
        case .assessmentAgent:
            return "chart.bar.fill"
        case .parentCommunicator:
            return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor:
            return "brain.head.profile"
        case .creativeMentor:
            return "paintbrush.fill"
        case .socialSkillsCoach:
            return "person.3.fill"
        }
    }

    private func iconForSubjectCategory(_ category: SubjectCategory) -> String {
        switch category {
        case .core:
            return "book.fill"
        case .language:
            return "globe"
        case .arts:
            return "paintbrush.fill"
        case .stem:
            return "atom"
        case .socialStudies:
            return "building.columns.fill"
        case .physicalEducation:
            return "figure.run"
        case .specialNeeds:
            return "heart.fill"
        case .ap:
            return "star.fill"
        case .elective:
            return "sparkles"
        case .lifeSkills:
            return "house.fill"
        }
    }
}

// MARK: - Supporting Views

struct SubjectCategoryFilterChip: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? .blue : .clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.blue, lineWidth: 1)
                    )
            )
            .foregroundColor(isSelected ? .white : .blue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AITeacherCard: View {
    let teacher: AIAgent
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Teacher Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: agentTypeGradient(for: teacher.agentType),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)

                    Image(systemName: agentTypeIcon(for: teacher.agentType))
                        .font(.title2)
                        .foregroundColor(.white)

                    // Status indicator
                    if teacher.isActive {
                        Circle()
                            .fill(.green)
                            .frame(width: 16, height: 16)
                            .overlay(
                                Circle()
                                    .stroke(.white, lineWidth: 2)
                            )
                            .offset(x: 20, y: 20)
                    }
                }

                // Teacher Info
                VStack(spacing: 4) {
                    Text(teacher.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .lineLimit(1)

                    Text(teacher.specialization)
                        .font(.caption)
                        .foregroundColor(.blue)
                        .lineLimit(1)

                    Text(teacher.gradeRange)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }

                // Special Needs Support Indicator
                if !teacher.specialNeedsAdaptations.isEmpty {
                    HStack(spacing: 4) {
                        Image(systemName: "heart.fill")
                            .font(.caption2)
                            .foregroundColor(.pink)

                        Text("Special Support")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.blue.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func agentTypeGradient(for agentType: AgentType) -> [Color] {
        switch agentType {
        case .subjectSpecialist:
            return [.blue, .cyan]
        case .learningCoach:
            return [.green, .mint]
        case .emotionalSupport:
            return [.pink, .red]
        case .assessmentAgent:
            return [.purple, .blue]
        case .parentCommunicator:
            return [.orange, .yellow]
        case .adaptiveTutor:
            return [.teal, .blue]
        case .creativeMentor:
            return [.pink, .purple]
        case .socialSkillsCoach:
            return [.indigo, .purple]
        }
    }

    private func agentTypeIcon(for agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist:
            return "graduationcap.fill"
        case .learningCoach:
            return "person.crop.circle.badge.checkmark"
        case .emotionalSupport:
            return "heart.fill"
        case .assessmentAgent:
            return "chart.bar.fill"
        case .parentCommunicator:
            return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor:
            return "brain.head.profile"
        case .creativeMentor:
            return "paintbrush.fill"
        case .socialSkillsCoach:
            return "person.3.fill"
        }
    }

    private func stateColor(for state: AgentState) -> Color {
        switch state {
        case .idle:
            return .gray
        case .teaching:
            return .green
        case .assessing:
            return .blue
        case .planning:
            return .orange
        case .collaborating:
            return .purple
        case .reflecting:
            return .yellow
        case .adapting:
            return .teal
        case .communicating:
            return .pink
        }
    }
}

// MARK: - Placeholder Views (to be implemented)

struct CreateAgentView: View {
    let aiAgentService: AIAgentService

    var body: some View {
        Text("Create Agent View - To be implemented")
    }
}

struct CrewBuilderView: View {
    let aiAgentService: AIAgentService
    let availableAgents: [AIAgent]

    var body: some View {
        Text("Crew Builder View - To be implemented")
    }
}

struct AgentDetailView: View {
    let agent: AIAgent
    let aiAgentService: AIAgentService

    var body: some View {
        Text("Agent Detail View - To be implemented")
    }
}
