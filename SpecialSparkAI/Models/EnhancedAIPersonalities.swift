//
//  EnhancedAIPersonalities.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Enhanced AI Teacher Personalities

struct AITeacherPersonality: Identifiable, Codable {
    let id: UUID
    let name: String
    let title: String
    let avatar: String
    let voiceProfile: PersonalityVoiceProfile
    let teachingStyle: TeachingStyle
    let specializations: [String]
    let personalityTraits: [PersonalityTrait]
    let communicationStyle: CommunicationStyle
    let adaptationCapabilities: [AdaptationCapability]
    let culturalBackground: CulturalBackground
    let languageSupport: [LanguageSupport]
    let specialNeedsExpertise: [SpecialNeedsExpertise]
    let motivationalApproach: MotivationalApproach
    let feedbackStyle: FeedbackStyle
    let interactionPreferences: InteractionPreferences
    let backstory: String
    let catchphrases: [String]
    let isActive: Bool
}

// Simple VoiceProfile for personality definitions (different from SwiftData VoiceProfile in AITeacherModels.swift)
struct PersonalityVoiceProfile: Codable {
    let voiceId: String
    let accent: String
    let speed: String
    let pitch: String
    let emotion: VoiceEmotion
    let clarity: VoiceClarity
    let warmth: Double // 0.0 to 1.0
    let energy: Double // 0.0 to 1.0
}

enum VoiceEmotion: String, CaseIterable, Codable {
    case neutral = "Neutral"
    case cheerful = "Cheerful"
    case calm = "Calm"
    case enthusiastic = "Enthusiastic"
    case empathetic = "Empathetic"
    case encouraging = "Encouraging"
}

enum VoiceClarity: String, CaseIterable, Codable {
    case standard = "Standard"
    case enhanced = "Enhanced"
    case therapeutic = "Therapeutic"
}

struct TeachingStyle: Codable {
    let primaryApproach: TeachingApproach
    let secondaryApproaches: [TeachingApproach]
    let adaptabilityLevel: Double // 0.0 to 1.0
    let scaffoldingPreference: ScaffoldingType
    let assessmentFrequency: AssessmentFrequency
    let feedbackTiming: FeedbackTiming
}

enum TeachingApproach: String, CaseIterable, Codable {
    case constructivist = "Constructivist"
    case behaviorist = "Behaviorist"
    case cognitivist = "Cognitivist"
    case humanistic = "Humanistic"
    case socialLearning = "Social Learning"
    case experiential = "Experiential"
    case inquiryBased = "Inquiry-Based"
    case projectBased = "Project-Based"
    case gameBased = "Game-Based"
    case storytelling = "Storytelling"
}

enum ScaffoldingType: String, CaseIterable, Codable {
    case minimal = "Minimal"
    case moderate = "Moderate"
    case extensive = "Extensive"
    case adaptive = "Adaptive"
}

enum AssessmentFrequency: String, CaseIterable, Codable {
    case continuous = "Continuous"
    case frequent = "Frequent"
    case moderate = "Moderate"
    case minimal = "Minimal"
}

enum FeedbackTiming: String, CaseIterable, Codable {
    case immediate = "Immediate"
    case delayed = "Delayed"
    case endOfSession = "End of Session"
    case adaptive = "Adaptive"
}

struct PersonalityTrait: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let intensity: Double // 0.0 to 1.0
    let manifestations: [String]
}

struct CommunicationStyle: Codable {
    let formality: FormalityLevel
    let directness: DirectnessLevel
    let emotionalExpression: EmotionalExpression
    let humorUsage: HumorUsage
    let encouragementStyle: EncouragementStyle
    let questioningTechnique: QuestioningTechnique
}

enum FormalityLevel: String, CaseIterable, Codable {
    case formal = "Formal"
    case semiformal = "Semi-formal"
    case casual = "Casual"
    case friendly = "Friendly"
}

enum DirectnessLevel: String, CaseIterable, Codable {
    case direct = "Direct"
    case moderate = "Moderate"
    case gentle = "Gentle"
    case subtle = "Subtle"
}

enum EmotionalExpression: String, CaseIterable, Codable {
    case high = "High"
    case moderate = "Moderate"
    case low = "Low"
    case adaptive = "Adaptive"
}

enum HumorUsage: String, CaseIterable, Codable {
    case frequent = "Frequent"
    case occasional = "Occasional"
    case rare = "Rare"
    case none = "None"
}

enum EncouragementStyle: String, CaseIterable, Codable {
    case enthusiastic = "Enthusiastic"
    case gentle = "Gentle"
    case achievement = "Achievement-focused"
    case effort = "Effort-focused"
    case growth = "Growth-focused"
}

enum QuestioningTechnique: String, CaseIterable, Codable {
    case socratic = "Socratic"
    case guided = "Guided Discovery"
    case openEnded = "Open-ended"
    case scaffolded = "Scaffolded"
}

struct AdaptationCapability: Identifiable, Codable {
    let id: UUID
    let type: AdaptationType
    let proficiency: Double // 0.0 to 1.0
    let triggers: [AdaptationTrigger]
    let strategies: [AdaptationStrategy]
}

enum AdaptationType: String, CaseIterable, Codable {
    case cognitive = "Cognitive"
    case emotional = "Emotional"
    case behavioral = "Behavioral"
    case sensory = "Sensory"
    case linguistic = "Linguistic"
    case cultural = "Cultural"
}

struct AdaptationTrigger: Identifiable, Codable {
    let id: UUID
    let condition: String
    let threshold: Double
    let response: String
}

struct AdaptationStrategy: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let implementation: String
    let effectiveness: Double
}

struct CulturalBackground: Codable {
    let primaryCulture: String
    let secondaryCultures: [String]
    let culturalValues: [String]
    let traditions: [String]
    let celebrations: [String]
    let storytellingTraditions: [String]
}

struct LanguageSupport: Identifiable, Codable {
    let id: UUID
    let language: String
    let proficiency: LanguageProficiency
    let dialects: [String]
    let culturalNuances: [String]
    let isNative: Bool
}

enum LanguageProficiency: String, CaseIterable, Codable {
    case native = "Native"
    case fluent = "Fluent"
    case advanced = "Advanced"
    case intermediate = "Intermediate"
    case basic = "Basic"
}

struct SpecialNeedsExpertise: Identifiable, Codable {
    let id: UUID
    let needType: SpecialNeedType
    let expertiseLevel: ExpertiseLevel
    let certifications: [String]
    let specializedStrategies: [String]
    let assistiveTechProficiency: [String]
}

enum ExpertiseLevel: String, CaseIterable, Codable {
    case expert = "Expert"
    case advanced = "Advanced"
    case intermediate = "Intermediate"
    case basic = "Basic"
}

struct MotivationalApproach: Codable {
    let primaryMotivators: [Motivator]
    let rewardSystems: [RewardSystem]
    let goalSettingStyle: GoalSettingStyle
    let celebrationStyle: CelebrationStyle
    let resilienceBuilding: ResilienceStrategy
}

enum Motivator: String, CaseIterable, Codable {
    case intrinsic = "Intrinsic"
    case achievement = "Achievement"
    case social = "Social"
    case curiosity = "Curiosity"
    case mastery = "Mastery"
    case autonomy = "Autonomy"
    case purpose = "Purpose"
}

struct RewardSystem: Identifiable, Codable {
    let id: UUID
    let type: RewardType
    let frequency: RewardFrequency
    let criteria: [String]
    let customization: [String: String]
}

enum RewardFrequency: String, CaseIterable, Codable {
    case immediate = "Immediate"
    case session = "Per Session"
    case daily = "Daily"
    case weekly = "Weekly"
    case milestone = "Milestone-based"
}

enum GoalSettingStyle: String, CaseIterable, Codable {
    case collaborative = "Collaborative"
    case guided = "Guided"
    case studentLed = "Student-led"
    case adaptive = "Adaptive"
}

enum CelebrationStyle: String, CaseIterable, Codable {
    case enthusiastic = "Enthusiastic"
    case quiet = "Quiet"
    case publicCelebration = "Public"
    case privateCelebration = "Private"
    case customized = "Customized"
}

struct ResilienceStrategy: Codable {
    let techniques: [String]
    let mindsetApproach: MindsetApproach
    let copingStrategies: [String]
    let supportMechanisms: [String]
}

enum MindsetApproach: String, CaseIterable, Codable {
    case growth = "Growth Mindset"
    case fixed = "Fixed Mindset"
    case balanced = "Balanced"
    case adaptive = "Adaptive"
}

struct FeedbackStyle: Codable {
    let tone: FeedbackTone
    let specificity: FeedbackSpecificity
    let focus: FeedbackFocus
    let delivery: FeedbackDelivery
    let frequency: FeedbackFrequency
}

enum FeedbackTone: String, CaseIterable, Codable {
    case encouraging = "Encouraging"
    case constructive = "Constructive"
    case gentle = "Gentle"
    case direct = "Direct"
    case supportive = "Supportive"
}

enum FeedbackSpecificity: String, CaseIterable, Codable {
    case detailed = "Detailed"
    case moderate = "Moderate"
    case general = "General"
    case adaptive = "Adaptive"
}

enum FeedbackFocus: String, CaseIterable, Codable {
    case process = "Process"
    case effort = "Effort"
    case strategy = "Strategy"
    case outcome = "Outcome"
    case growth = "Growth"
}

enum FeedbackDelivery: String, CaseIterable, Codable {
    case verbal = "Verbal"
    case visual = "Visual"
    case written = "Written"
    case multimodal = "Multimodal"
}

enum FeedbackFrequency: String, CaseIterable, Codable {
    case continuous = "Continuous"
    case frequent = "Frequent"
    case moderate = "Moderate"
    case minimal = "Minimal"
}

struct InteractionPreferences: Codable {
    let sessionLength: SessionLength
    let breakFrequency: BreakFrequency
    let interactionStyle: InteractionStyle
    let attentionManagement: AttentionManagement
    let transitionStyle: TransitionStyle
}

enum SessionLength: String, CaseIterable, Codable {
    case short = "Short (10-15 min)"
    case medium = "Medium (20-30 min)"
    case long = "Long (45-60 min)"
    case adaptive = "Adaptive"
}

enum BreakFrequency: String, CaseIterable, Codable {
    case frequent = "Frequent"
    case moderate = "Moderate"
    case minimal = "Minimal"
    case asNeeded = "As Needed"
}

enum InteractionStyle: String, CaseIterable, Codable {
    case conversational = "Conversational"
    case structured = "Structured"
    case playful = "Playful"
    case formal = "Formal"
    case adaptive = "Adaptive"
}

enum AttentionManagement: String, CaseIterable, Codable {
    case high = "High Support"
    case moderate = "Moderate Support"
    case low = "Low Support"
    case adaptive = "Adaptive"
}

enum TransitionStyle: String, CaseIterable, Codable {
    case gradual = "Gradual"
    case clear = "Clear Signals"
    case routine = "Routine-based"
    case flexible = "Flexible"
}
