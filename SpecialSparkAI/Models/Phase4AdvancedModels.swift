//
//  Phase4AdvancedModels.swift
//  SpecialSparkAI
//
//  Phase 4: Advanced Features Models
//  - Real-time collaboration models
//  - Advanced analytics models
//  - Predictive learning models
//  - Multi-modal interaction models
//

import Foundation
import SwiftUI

// MARK: - Real-Time Collaboration Models

struct CollaborativeSession: Identifiable, Codable {
    let id: UUID
    var studentIds: [UUID]
    let subject: Subject
    let startTime: Date
    var endTime: Date?
    var status: SessionStatus
    var activities: [CollaborativeActivity]
    var sharedResources: [SharedResource]
    var chatMessages: [CollaborativeMessage]

    init(id: UUID, studentIds: [UUID], subject: Subject, startTime: Date, status: SessionStatus) {
        self.id = id
        self.studentIds = studentIds
        self.subject = subject
        self.startTime = startTime
        self.status = status
        self.activities = []
        self.sharedResources = []
        self.chatMessages = []
    }
}

enum SessionStatus: String, CaseIterable, Codable {
    case waiting = "Waiting"
    case active = "Active"
    case paused = "Paused"
    case completed = "Completed"
    case cancelled = "Cancelled"
}

struct CollaborativeActivity: Identifiable, Codable {
    let id: UUID
    let type: ActivityType
    let title: String
    let description: String
    let participants: [UUID]
    let startTime: Date
    var endTime: Date?
    var status: ActivityStatus
    var results: [ActivityResult]

    enum ActivityType: String, CaseIterable, Codable {
        case brainstorming = "Brainstorming"
        case problemSolving = "Problem Solving"
        case peerReview = "Peer Review"
        case groupProject = "Group Project"
        case discussion = "Discussion"
        case quiz = "Collaborative Quiz"
    }

    enum ActivityStatus: String, CaseIterable, Codable {
        case notStarted = "Not Started"
        case inProgress = "In Progress"
        case completed = "Completed"
        case cancelled = "Cancelled"
    }
}

struct ActivityResult: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let contribution: String
    let timestamp: Date
    let score: Double?
    let feedback: String?
}

struct SharedResource: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: ResourceType
    let url: String
    let uploadedBy: UUID
    let uploadedAt: Date
    let permissions: ResourcePermissions

    enum ResourceType: String, CaseIterable, Codable {
        case document = "Document"
        case image = "Image"
        case video = "Video"
        case audio = "Audio"
        case link = "Link"
        case whiteboard = "Whiteboard"
    }
}

struct ResourcePermissions: Codable {
    let canView: [UUID]
    let canEdit: [UUID]
    let canShare: [UUID]
    let isPublic: Bool
}

struct CollaborativeMessage: Identifiable, Codable {
    let id: UUID
    let senderId: UUID
    let content: String
    let timestamp: Date
    let type: MessageType
    let reactions: [MessageReaction]

    enum MessageType: String, CaseIterable, Codable {
        case text = "Text"
        case image = "Image"
        case file = "File"
        case emoji = "Emoji"
        case system = "System"
    }
}

struct MessageReaction: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let emoji: String
    let timestamp: Date
}

// MARK: - Advanced Analytics Models

struct AdvancedAnalytics: Codable {
    let totalStudents: Int
    let activeTeachers: Int
    let completedLessons: Int
    let averageEngagement: Double
    let learningEfficiency: Double
    let specialNeedsSupport: Double
    let parentSatisfaction: Double
    let trends: [AnalyticsTrend]
    let generatedAt: Date

    init(totalStudents: Int, activeTeachers: Int, completedLessons: Int, averageEngagement: Double, learningEfficiency: Double, specialNeedsSupport: Double, parentSatisfaction: Double, trends: [AnalyticsTrend]) {
        self.totalStudents = totalStudents
        self.activeTeachers = activeTeachers
        self.completedLessons = completedLessons
        self.averageEngagement = averageEngagement
        self.learningEfficiency = learningEfficiency
        self.specialNeedsSupport = specialNeedsSupport
        self.parentSatisfaction = parentSatisfaction
        self.trends = trends
        self.generatedAt = Date()
    }
}

struct AnalyticsTrend: Identifiable, Codable {
    let id = UUID()
    let metric: String
    let value: Double
    let change: Double
    let period: String
    let direction: TrendDirection

    init(metric: String, value: Double, change: Double, period: String) {
        self.metric = metric
        self.value = value
        self.change = change
        self.period = period
        self.direction = change > 0 ? .up : change < 0 ? .down : .stable
    }

    enum TrendDirection: String, CaseIterable, Codable {
        case up = "Up"
        case down = "Down"
        case stable = "Stable"
    }
}

struct StudentAnalytics: Identifiable, Codable {
    let id = UUID()
    let studentId: UUID
    let learningVelocity: Double
    let comprehensionRate: Double
    let engagementLevel: Double
    let strugglingAreas: [String]
    let strengths: [String]
    let recommendedInterventions: [String]
    let progressTrend: ProgressTrend
    let generatedAt: Date

    init(studentId: UUID, learningVelocity: Double, comprehensionRate: Double, engagementLevel: Double, strugglingAreas: [String], strengths: [String], recommendedInterventions: [String], progressTrend: ProgressTrend) {
        self.studentId = studentId
        self.learningVelocity = learningVelocity
        self.comprehensionRate = comprehensionRate
        self.engagementLevel = engagementLevel
        self.strugglingAreas = strugglingAreas
        self.strengths = strengths
        self.recommendedInterventions = recommendedInterventions
        self.progressTrend = progressTrend
        self.generatedAt = Date()
    }

    enum ProgressTrend: String, CaseIterable, Codable {
        case improving = "Improving"
        case stable = "Stable"
        case declining = "Declining"
        case accelerating = "Accelerating"
    }
}

struct ClassroomAnalytics: Identifiable, Codable {
    let id = UUID()
    let classroomId: UUID
    let averagePerformance: Double
    let engagementMetrics: EngagementMetrics
    let collaborationMetrics: CollaborationMetrics
    let specialNeedsMetrics: SpecialNeedsMetrics
    let generatedAt: Date

    init(classroomId: UUID, averagePerformance: Double, engagementMetrics: EngagementMetrics, collaborationMetrics: CollaborationMetrics) {
        self.classroomId = classroomId
        self.averagePerformance = averagePerformance
        self.engagementMetrics = engagementMetrics
        self.collaborationMetrics = collaborationMetrics
        self.specialNeedsMetrics = SpecialNeedsMetrics(
            supportedStudents: 15,
            accommodationUsage: 0.92,
            successRate: 0.89,
            interventionEffectiveness: 0.87
        )
        self.generatedAt = Date()
    }
}

struct EngagementMetrics: Codable {
    let averageSessionTime: Int // minutes
    let interactionRate: Double
    let completionRate: Double
    let attentionSpan: Double
    let participationLevel: Double

    init(averageSessionTime: Int, interactionRate: Double, completionRate: Double) {
        self.averageSessionTime = averageSessionTime
        self.interactionRate = interactionRate
        self.completionRate = completionRate
        self.attentionSpan = 0.85
        self.participationLevel = 0.78
    }
}

struct CollaborationMetrics: Codable {
    let peerInteractions: Int
    let groupProjects: Int
    let helpRequests: Int
    let knowledgeSharing: Double
    let teamworkSkills: Double

    init(peerInteractions: Int, groupProjects: Int, helpRequests: Int) {
        self.peerInteractions = peerInteractions
        self.groupProjects = groupProjects
        self.helpRequests = helpRequests
        self.knowledgeSharing = 0.82
        self.teamworkSkills = 0.79
    }
}

struct SpecialNeedsMetrics: Codable {
    let supportedStudents: Int
    let accommodationUsage: Double
    let successRate: Double
    let interventionEffectiveness: Double
    let adaptationAccuracy: Double

    init(supportedStudents: Int, accommodationUsage: Double, successRate: Double, interventionEffectiveness: Double) {
        self.supportedStudents = supportedStudents
        self.accommodationUsage = accommodationUsage
        self.successRate = successRate
        self.interventionEffectiveness = interventionEffectiveness
        self.adaptationAccuracy = 0.91
    }
}

struct SchoolAnalytics: Identifiable, Codable {
    let id = UUID()
    let totalEnrollment: Int
    let academicPerformance: Double
    let specialNeedsSuccess: Double
    let parentEngagement: Double
    let teacherEffectiveness: Double
    let resourceUtilization: Double
    let costEfficiency: Double
    let studentSatisfaction: Double
    let generatedAt: Date

    init(totalEnrollment: Int, academicPerformance: Double, specialNeedsSuccess: Double, parentEngagement: Double, teacherEffectiveness: Double, resourceUtilization: Double) {
        self.totalEnrollment = totalEnrollment
        self.academicPerformance = academicPerformance
        self.specialNeedsSuccess = specialNeedsSuccess
        self.parentEngagement = parentEngagement
        self.teacherEffectiveness = teacherEffectiveness
        self.resourceUtilization = resourceUtilization
        self.costEfficiency = 0.88
        self.studentSatisfaction = 0.91
        self.generatedAt = Date()
    }
}

// MARK: - Predictive Learning Models

struct PredictiveLearningInsight: Identifiable, Codable {
    let id = UUID()
    let type: InsightType
    let description: String
    let confidence: Double
    let actionable: Bool
    let priority: Priority
    let generatedAt: Date

    init(type: InsightType, description: String, confidence: Double, actionable: Bool) {
        self.type = type
        self.description = description
        self.confidence = confidence
        self.actionable = actionable
        self.priority = confidence > 0.9 ? .high : confidence > 0.7 ? .medium : .low
        self.generatedAt = Date()
    }

    enum InsightType: String, CaseIterable, Codable {
        case performancePrediction = "Performance Prediction"
        case interventionRecommendation = "Intervention Recommendation"
        case learningPathOptimization = "Learning Path Optimization"
        case riskIdentification = "Risk Identification"
        case resourceAllocation = "Resource Allocation"
    }

    enum Priority: String, CaseIterable, Codable {
        case low = "Low"
        case medium = "Medium"
        case high = "High"
        case critical = "Critical"
    }
}

struct PerformancePrediction: Identifiable, Codable {
    let id = UUID()
    let studentId: UUID
    let subject: Subject
    let predictedGrade: String
    let confidence: Double
    let timeframe: String
    let factors: [String]
    let recommendations: [String]
    let generatedAt: Date

    init(studentId: UUID, subject: Subject, predictedGrade: String, confidence: Double, timeframe: String, factors: [String], recommendations: [String]) {
        self.studentId = studentId
        self.subject = subject
        self.predictedGrade = predictedGrade
        self.confidence = confidence
        self.timeframe = timeframe
        self.factors = factors
        self.recommendations = recommendations
        self.generatedAt = Date()
    }
}

struct LearningPathRecommendation: Identifiable, Codable {
    let id = UUID()
    let studentId: UUID
    let recommendedSubjects: [String]
    let skillGaps: [String]
    let learningStyle: String
    let estimatedCompletion: String
    let milestones: [String]
    let adaptations: [String]
    let generatedAt: Date

    init(studentId: UUID, recommendedSubjects: [String], skillGaps: [String], learningStyle: String, estimatedCompletion: String, milestones: [String]) {
        self.studentId = studentId
        self.recommendedSubjects = recommendedSubjects
        self.skillGaps = skillGaps
        self.learningStyle = learningStyle
        self.estimatedCompletion = estimatedCompletion
        self.milestones = milestones
        self.adaptations = ["Visual aids", "Extended time", "Frequent breaks"]
        self.generatedAt = Date()
    }
}

struct AtRiskStudent: Identifiable, Codable {
    let id = UUID()
    let studentId: UUID
    let riskLevel: RiskLevel
    let factors: [String]
    let recommendedActions: [String]
    let timeline: String
    let confidence: Double
    let identifiedAt: Date

    init(studentId: UUID, riskLevel: RiskLevel, factors: [String], recommendedActions: [String], timeline: String) {
        self.studentId = studentId
        self.riskLevel = riskLevel
        self.factors = factors
        self.recommendedActions = recommendedActions
        self.timeline = timeline
        self.confidence = 0.85
        self.identifiedAt = Date()
    }

    enum RiskLevel: String, CaseIterable, Codable {
        case low = "Low"
        case moderate = "Moderate"
        case high = "High"
        case critical = "Critical"
    }
}

// MARK: - Multi-Modal Interaction Models

struct MultiModalInteraction: Identifiable, Codable {
    let id = UUID()
    let type: InteractionType
    let isEnabled: Bool
    let description: String
    let capabilities: [String]
    let requirements: [String]

    init(type: InteractionType, isEnabled: Bool, description: String) {
        self.type = type
        self.isEnabled = isEnabled
        self.description = description
        self.capabilities = type.defaultCapabilities
        self.requirements = type.defaultRequirements
    }

    enum InteractionType: String, CaseIterable, Codable {
        case voice = "Voice"
        case gesture = "Gesture"
        case visual = "Visual"
        case haptic = "Haptic"
        case eye = "Eye Tracking"
        case brain = "Brain-Computer Interface"

        var defaultCapabilities: [String] {
            switch self {
            case .voice:
                return ["Speech recognition", "Natural language processing", "Voice synthesis"]
            case .gesture:
                return ["Hand tracking", "Gesture recognition", "Motion analysis"]
            case .visual:
                return ["Object detection", "Facial recognition", "Scene understanding"]
            case .haptic:
                return ["Touch feedback", "Vibration patterns", "Force feedback"]
            case .eye:
                return ["Gaze tracking", "Attention monitoring", "Focus analysis"]
            case .brain:
                return ["EEG monitoring", "Cognitive load assessment", "Attention detection"]
            }
        }

        var defaultRequirements: [String] {
            switch self {
            case .voice:
                return ["Microphone", "Speaker", "Internet connection"]
            case .gesture:
                return ["Camera", "Motion sensors", "Processing power"]
            case .visual:
                return ["Camera", "Image processing", "Machine learning models"]
            case .haptic:
                return ["Haptic device", "Touch sensors", "Actuators"]
            case .eye:
                return ["Eye tracking camera", "Calibration", "Head stabilization"]
            case .brain:
                return ["EEG headset", "Signal processing", "Specialized software"]
            }
        }
    }
}

struct MultiModalInput: Codable {
    let voiceData: Data?
    let gestureData: GestureData?
    let visualData: Data?
    let hapticData: HapticData?
    let eyeData: EyeTrackingData?
    let timestamp: Date
    let context: String

    init(voiceData: Data? = nil, gestureData: GestureData? = nil, visualData: Data? = nil, hapticData: HapticData? = nil, eyeData: EyeTrackingData? = nil, context: String = "") {
        self.voiceData = voiceData
        self.gestureData = gestureData
        self.visualData = visualData
        self.hapticData = hapticData
        self.eyeData = eyeData
        self.timestamp = Date()
        self.context = context
    }
}

struct MultiModalResponse: Codable {
    let textResponse: String
    let audioResponse: Data?
    let visualResponse: String?
    let hapticFeedback: HapticFeedback
    let adaptations: [String]
    let confidence: Double
    let generatedAt: Date

    init(textResponse: String, audioResponse: Data?, visualResponse: String?, hapticFeedback: HapticFeedback, adaptations: [String]) {
        self.textResponse = textResponse
        self.audioResponse = audioResponse
        self.visualResponse = visualResponse
        self.hapticFeedback = hapticFeedback
        self.adaptations = adaptations
        self.confidence = 0.88
        self.generatedAt = Date()
    }
}

struct GestureData: Codable {
    let handPositions: [HandPosition]
    let gestureType: GestureType
    let confidence: Double
    let timestamp: Date

    init(handPositions: [HandPosition], gestureType: GestureType, confidence: Double) {
        self.handPositions = handPositions
        self.gestureType = gestureType
        self.confidence = confidence
        self.timestamp = Date()
    }
}

struct HandPosition: Codable {
    let x: Double
    let y: Double
    let z: Double
    let landmarks: [CGPoint]
}

enum GestureType: String, CaseIterable, Codable {
    case pointingUp = "Pointing Up"
    case thumbsUp = "Thumbs Up"
    case waving = "Waving"
    case openPalm = "Open Palm"
    case fist = "Fist"
    case peace = "Peace Sign"
    case counting = "Counting"
    case writing = "Writing Motion"
}

struct HapticData: Codable {
    let intensity: Double
    let duration: Double
    let pattern: HapticPattern
    let timestamp: Date

    init(intensity: Double, duration: Double, pattern: HapticPattern) {
        self.intensity = intensity
        self.duration = duration
        self.pattern = pattern
        self.timestamp = Date()
    }
}

enum HapticPattern: String, CaseIterable, Codable {
    case tap = "Tap"
    case pulse = "Pulse"
    case vibration = "Vibration"
    case rhythm = "Rhythm"
    case custom = "Custom"
}

enum HapticFeedback: String, CaseIterable, Codable {
    case success = "Success"
    case error = "Error"
    case warning = "Warning"
    case notification = "Notification"
    case selection = "Selection"
    case none = "None"
}

struct EyeTrackingData: Codable {
    let gazePoint: CGPoint
    let pupilDilation: Double
    let blinkRate: Double
    let fixationDuration: Double
    let timestamp: Date

    init(gazePoint: CGPoint, pupilDilation: Double, blinkRate: Double, fixationDuration: Double) {
        self.gazePoint = gazePoint
        self.pupilDilation = pupilDilation
        self.blinkRate = blinkRate
        self.fixationDuration = fixationDuration
        self.timestamp = Date()
    }
}

struct VoiceInteractionResult: Codable {
    let transcription: String
    let intent: VoiceIntent
    let confidence: Double
    let response: String
    let audioResponse: Data
    let emotions: [EmotionDetection]
    let processedAt: Date

    init(transcription: String, intent: VoiceIntent, confidence: Double, response: String, audioResponse: Data) {
        self.transcription = transcription
        self.intent = intent
        self.confidence = confidence
        self.response = response
        self.audioResponse = audioResponse
        self.emotions = []
        self.processedAt = Date()
    }
}

enum VoiceIntent: String, CaseIterable, Codable {
    case helpRequest = "Help Request"
    case question = "Question"
    case answer = "Answer"
    case command = "Command"
    case greeting = "Greeting"
    case feedback = "Feedback"
    case clarification = "Clarification"
}

struct EmotionDetection: Codable {
    let emotion: Emotion
    let confidence: Double
    let intensity: Double

    enum Emotion: String, CaseIterable, Codable {
        case happy = "Happy"
        case sad = "Sad"
        case frustrated = "Frustrated"
        case excited = "Excited"
        case confused = "Confused"
        case confident = "Confident"
        case neutral = "Neutral"
    }
}

struct GestureInteractionResult: Codable {
    let recognizedGesture: GestureType
    let confidence: Double
    let interpretation: String
    let response: String
    let suggestedActions: [String]
    let processedAt: Date

    init(recognizedGesture: GestureType, confidence: Double, interpretation: String, response: String) {
        self.recognizedGesture = recognizedGesture
        self.confidence = confidence
        self.interpretation = interpretation
        self.response = response
        self.suggestedActions = []
        self.processedAt = Date()
    }
}

struct VisualInteractionResult: Codable {
    let recognizedObjects: [String]
    let confidence: Double
    let analysis: String
    let suggestions: [String]
    let detectedEmotions: [EmotionDetection]
    let processedAt: Date

    init(recognizedObjects: [String], confidence: Double, analysis: String, suggestions: [String]) {
        self.recognizedObjects = recognizedObjects
        self.confidence = confidence
        self.analysis = analysis
        self.suggestions = suggestions
        self.detectedEmotions = []
        self.processedAt = Date()
    }
}
