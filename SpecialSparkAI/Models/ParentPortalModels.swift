//
//  ParentPortalModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Parent Portal Models

struct ParentProfile: Identifiable, Codable {
    let id: UUID
    let firstName: String
    let lastName: String
    let email: String
    let phoneNumber: String?
    let relationship: ParentRelationship
    let children: [UUID] // Student IDs
    let preferences: ParentPreferences
    let communicationSettings: CommunicationSettings
    let accessLevel: AccessLevel
    let isActive: Bool
    let createdAt: String
    let lastLogin: String?
}

enum ParentRelationship: String, CaseIterable, Codable {
    case mother = "Mother"
    case father = "Father"
    case guardian = "Guardian"
    case stepParent = "Step-parent"
    case grandparent = "Grandparent"
    case caregiver = "Caregiver"
    case other = "Other"
}

struct ParentPreferences: Codable {
    let language: String
    let timezone: String
    let reportFrequency: ReportFrequency
    let notificationTypes: [NotificationType]
    let dashboardLayout: DashboardLayout
    let dataVisualization: DataVisualizationPreference
}

enum ReportFrequency: String, CaseIterable, Codable {
    case daily = "Daily"
    case weekly = "Weekly"
    case biweekly = "Bi-weekly"
    case monthly = "Monthly"
    case asNeeded = "As Needed"
}

enum NotificationType: String, CaseIterable, Codable {
    case achievements = "Achievements"
    case concerns = "Concerns"
    case progress = "Progress Updates"
    case assignments = "Assignments"
    case behavior = "Behavior Reports"
    case attendance = "Attendance"
    case meetings = "Meeting Reminders"
    case system = "System Updates"
}

struct CommunicationSettings: Codable {
    let preferredMethod: CommunicationMethod
    let availableHours: AvailabilityWindow
    let emergencyContact: Bool
    let autoResponders: [AutoResponder]
    let messageFilters: [MessageFilter]
}

enum CommunicationMethod: String, CaseIterable, Codable {
    case email = "Email"
    case sms = "SMS"
    case app = "In-App"
    case phone = "Phone"
    case video = "Video Call"
}

struct AvailabilityWindow: Codable {
    let startTime: String
    let endTime: String
    let timezone: String
    let weekdays: [Int] // 0-6, Sunday = 0
}

struct AutoResponder: Identifiable, Codable {
    let id: UUID
    let trigger: String
    let response: String
    let isActive: Bool
}

struct MessageFilter: Identifiable, Codable {
    let id: UUID
    let type: FilterType
    let criteria: String
    let action: FilterAction
}

enum FilterType: String, CaseIterable, Codable {
    case keyword = "Keyword"
    case sender = "Sender"
    case priority = "Priority"
    case category = "Category"
}

enum FilterAction: String, CaseIterable, Codable {
    case highlight = "Highlight"
    case categorize = "Categorize"
    case forward = "Forward"
    case archive = "Archive"
}

enum AccessLevel: String, CaseIterable, Codable {
    case full = "Full Access"
    case limited = "Limited Access"
    case view = "View Only"
    case emergency = "Emergency Only"
}

enum DashboardLayout: String, CaseIterable, Codable {
    case overview = "Overview"
    case detailed = "Detailed"
    case simplified = "Simplified"
    case custom = "Custom"
}

struct DataVisualizationPreference: Codable {
    let chartTypes: [ChartType]
    let colorScheme: ColorScheme
    let animationsEnabled: Bool
    let detailLevel: DetailLevel
}

enum ChartType: String, CaseIterable, Codable {
    case line = "Line Chart"
    case bar = "Bar Chart"
    case pie = "Pie Chart"
    case progress = "Progress Bar"
    case heatmap = "Heat Map"
}

enum ColorScheme: String, CaseIterable, Codable {
    case standard = "Standard"
    case highContrast = "High Contrast"
    case colorBlind = "Color Blind Friendly"
    case monochrome = "Monochrome"
}

enum DetailLevel: String, CaseIterable, Codable {
    case high = "High Detail"
    case medium = "Medium Detail"
    case low = "Low Detail"
    case summary = "Summary Only"
}

// MARK: - Parent Dashboard Data

struct ParentDashboard: Codable {
    let parentId: UUID
    let children: [ChildSummary]
    let recentActivity: [ActivitySummary]
    let upcomingEvents: [UpcomingEvent]
    let alerts: [ParentAlert]
    let progressOverview: ProgressOverview
    let communicationSummary: CommunicationSummary
    let recommendations: [ParentRecommendation]
    let lastUpdated: String
}

struct ChildSummary: Identifiable, Codable {
    let id: UUID
    let name: String
    let grade: String
    let overallProgress: Double
    let currentMood: String
    let todaysActivities: Int
    let weeklyGoalProgress: Double
    let recentAchievements: [String]
    let areasOfConcern: [String]
    let nextSession: String?
}

struct ActivitySummary: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let studentName: String
    let activity: String
    let subject: String
    let duration: Int
    let performance: Double
    let timestamp: String
    let highlights: [String]
}

struct UpcomingEvent: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: EventType
    let scheduledTime: String
    let participants: [String]
    let isRequired: Bool
    let reminderSet: Bool
}

enum EventType: String, CaseIterable, Codable {
    case assessment = "Assessment"
    case meeting = "Meeting"
    case presentation = "Presentation"
    case fieldTrip = "Field Trip"
    case workshop = "Workshop"
    case conference = "Conference"
}

struct ParentAlert: Identifiable, Codable {
    let id: UUID
    let type: AlertType
    let priority: AlertPriority
    let title: String
    let message: String
    let studentId: UUID
    let actionRequired: Bool
    let possibleActions: [AlertAction]
    let timestamp: String
    let isRead: Bool
}

enum AlertType: String, CaseIterable, Codable {
    case achievement = "Achievement"
    case concern = "Concern"
    case behavior = "Behavior"
    case academic = "Academic"
    case health = "Health"
    case attendance = "Attendance"
    case system = "System"
}

enum AlertPriority: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case urgent = "Urgent"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .urgent: return .red
        }
    }
}

struct AlertAction: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: ActionType
    let url: String?
}

enum ActionType: String, CaseIterable, Codable {
    case acknowledge = "Acknowledge"
    case respond = "Respond"
    case schedule = "Schedule Meeting"
    case contact = "Contact Teacher"
    case review = "Review Details"
    case approve = "Approve"
    case decline = "Decline"
}

struct ProgressOverview: Codable {
    let overallGrade: String
    let subjectProgress: [SubjectProgress]
    let skillDevelopment: [SkillProgress]
    let behavioralProgress: BehavioralProgress
    let socialProgress: SocialProgress
    let trends: [ProgressTrend]
}

struct SubjectProgress: Identifiable, Codable {
    let id: UUID
    let subject: String
    let currentGrade: String
    let progressPercentage: Double
    let timeSpent: Int
    let strengths: [String]
    let improvementAreas: [String]
    let recentAssessments: [AssessmentResult]
}

struct AssessmentResult: Identifiable, Codable {
    let id: UUID
    let name: String
    let score: Double
    let maxScore: Double
    let date: String
    let feedback: String
}

struct SkillProgress: Identifiable, Codable {
    let id: UUID
    let skillName: String
    let currentLevel: String
    let progressPercentage: Double
    let milestones: [Milestone]
    let nextGoals: [String]
}

struct Milestone: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let achievedDate: String?
    let isCompleted: Bool
}

struct BehavioralProgress: Codable {
    let overallRating: Double
    let positiveIncidents: Int
    let concerningIncidents: Int
    let improvementAreas: [String]
    let strengths: [String]
    let interventions: [Intervention]
}

struct Intervention: Identifiable, Codable {
    let id: UUID
    let type: String
    let description: String
    let startDate: String
    let effectiveness: Double?
    let status: InterventionStatus
}

enum InterventionStatus: String, CaseIterable, Codable {
    case planned = "Planned"
    case active = "Active"
    case completed = "Completed"
    case discontinued = "Discontinued"
}

struct SocialProgress: Codable {
    let peerInteractions: Double
    let communicationSkills: Double
    let collaborationSkills: Double
    let empathy: Double
    let socialGoals: [SocialGoal]
}

struct SocialGoal: Identifiable, Codable {
    let id: UUID
    let goal: String
    let progress: Double
    let targetDate: String
    let strategies: [String]
}

struct ProgressTrend: Identifiable, Codable {
    let id: UUID
    let metric: String
    let direction: TrendDirection
    let magnitude: Double
    let timeframe: String
    let significance: TrendSignificance
}

enum TrendDirection: String, CaseIterable, Codable {
    case improving = "Improving"
    case declining = "Declining"
    case stable = "Stable"
    case fluctuating = "Fluctuating"
}

enum TrendSignificance: String, CaseIterable, Codable {
    case significant = "Significant"
    case moderate = "Moderate"
    case minor = "Minor"
    case negligible = "Negligible"
}

struct CommunicationSummary: Codable {
    let unreadMessages: Int
    let recentConversations: [ConversationSummary]
    let scheduledMeetings: [ScheduledMeeting]
    let communicationHistory: [CommunicationRecord]
}

struct ConversationSummary: Identifiable, Codable {
    let id: UUID
    let participant: String
    let lastMessage: String
    let timestamp: String
    let isUnread: Bool
    let priority: MessagePriority
}

enum MessagePriority: String, CaseIterable, Codable {
    case low = "Low"
    case normal = "Normal"
    case high = "High"
    case urgent = "Urgent"
}

struct ScheduledMeeting: Identifiable, Codable {
    let id: UUID
    let title: String
    let participants: [String]
    let scheduledTime: String
    let duration: Int
    let agenda: [String]
    let meetingLink: String?
}

struct CommunicationRecord: Identifiable, Codable {
    let id: UUID
    let type: CommunicationType
    let participant: String
    let subject: String
    let timestamp: String
    let summary: String
}

enum CommunicationType: String, CaseIterable, Codable {
    case message = "Message"
    case email = "Email"
    case call = "Phone Call"
    case meeting = "Meeting"
    case note = "Note"
}

struct ParentRecommendation: Identifiable, Codable {
    let id: UUID
    let type: RecommendationType
    let title: String
    let description: String
    let priority: RecommendationPriority
    let actionItems: [String]
    let resources: [Resource]
    let timeline: String
}

enum RecommendationType: String, CaseIterable, Codable {
    case academic = "Academic Support"
    case behavioral = "Behavioral"
    case social = "Social Skills"
    case therapeutic = "Therapeutic"
    case enrichment = "Enrichment"
    case technology = "Technology"
}

enum RecommendationPriority: String, CaseIterable, Codable {
    case immediate = "Immediate"
    case soon = "Soon"
    case moderate = "Moderate"
    case low = "Low"
}

struct Resource: Identifiable, Codable {
    let id: UUID
    let title: String
    let type: ResourceType
    let url: String?
    let description: String
}

enum ResourceType: String, CaseIterable, Codable {
    case article = "Article"
    case video = "Video"
    case book = "Book"
    case website = "Website"
    case app = "App"
    case course = "Course"
    case support = "Support Group"
}
