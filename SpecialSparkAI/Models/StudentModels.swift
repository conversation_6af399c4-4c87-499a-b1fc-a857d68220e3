import Foundation
import SwiftData

// MARK: - Grade and School Level Enums

enum GradeLevel: String, CaseIterable, Codable {
    case kindergarten = "K"
    case grade1 = "1", grade2 = "2", grade3 = "3", grade4 = "4", grade5 = "5"
    case grade6 = "6", grade7 = "7", grade8 = "8"
    case grade9 = "9", grade10 = "10", grade11 = "11", grade12 = "12"

    var displayName: String {
        switch self {
        case .kindergarten: return "Kindergarten"
        case .grade1: return "1st Grade"
        case .grade2: return "2nd Grade"
        case .grade3: return "3rd Grade"
        case .grade4: return "4th Grade"
        case .grade5: return "5th Grade"
        case .grade6: return "6th Grade"
        case .grade7: return "7th Grade"
        case .grade8: return "8th Grade"
        case .grade9: return "9th Grade"
        case .grade10: return "10th Grade"
        case .grade11: return "11th Grade"
        case .grade12: return "12th Grade"
        }
    }

    var schoolLevel: SchoolLevel {
        switch self {
        case .kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5:
            return .elementary
        case .grade6, .grade7, .grade8:
            return .middle
        case .grade9, .grade10, .grade11, .grade12:
            return .high
        }
    }
}

enum SchoolLevel: String, CaseIterable, Codable {
    case elementary = "elementary"
    case middle = "middle"
    case high = "high"

    var displayName: String {
        switch self {
        case .elementary: return "Elementary School"
        case .middle: return "Middle School"
        case .high: return "High School"
        }
    }

    var grades: [GradeLevel] {
        switch self {
        case .elementary:
            return [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5]
        case .middle:
            return [.grade6, .grade7, .grade8]
        case .high:
            return [.grade9, .grade10, .grade11, .grade12]
        }
    }
}

enum SpecialNeedsType: String, CaseIterable, Codable {
    case none = "none"
    case autism = "autism"
    case adhd = "adhd"
    case dyslexia = "dyslexia"
    case speechDelay = "speech_delay"
    case learningDisability = "learning_disability"
    case emotionalBehavioral = "emotional_behavioral"
    case intellectualDisability = "intellectual_disability"
    case physicalDisability = "physical_disability"

    var displayName: String {
        switch self {
        case .none: return "No Special Needs"
        case .autism: return "Autism Spectrum"
        case .adhd: return "ADHD"
        case .dyslexia: return "Dyslexia"
        case .speechDelay: return "Speech Delay"
        case .learningDisability: return "Learning Disability"
        case .emotionalBehavioral: return "Emotional/Behavioral"
        case .intellectualDisability: return "Intellectual Disability"
        case .physicalDisability: return "Physical Disability"
        }
    }
}

enum SubjectCategory: String, CaseIterable, Codable {
    case core = "core"
    case language = "language"
    case arts = "arts"
    case stem = "stem"
    case socialStudies = "social_studies"
    case physicalEducation = "physical_education"
    case specialNeeds = "special_needs"
    case ap = "ap"
    case elective = "elective"
    case lifeSkills = "life_skills"

    var displayName: String {
        switch self {
        case .core: return "Core Subjects"
        case .language: return "World Languages"
        case .arts: return "Arts & Music"
        case .stem: return "STEM"
        case .socialStudies: return "Social Studies"
        case .physicalEducation: return "Physical Education"
        case .specialNeeds: return "Special Needs Support"
        case .ap: return "Advanced Placement"
        case .elective: return "Electives"
        case .lifeSkills: return "Life Skills"
        }
    }
}

// MARK: - Student Model

@Model
class Student {
    var id: UUID
    var userId: UUID?
    var firstName: String
    var lastName: String
    var gradeLevel: GradeLevel
    var schoolLevel: SchoolLevel
    var dateOfBirth: Date?
    var specialNeeds: [SpecialNeedsType]
    var learningPreferences: [String: String]
    var enrollmentDate: Date
    var parentEmail: String
    var parentPhone: String
    var isActive: Bool
    var achievements: [GameAchievement]

    init(firstName: String, lastName: String, gradeLevel: GradeLevel, specialNeeds: [SpecialNeedsType] = [.none], learningPreferences: [String: String] = [:], parentEmail: String = "", parentPhone: String = "") {
        self.id = UUID()
        self.userId = nil
        self.firstName = firstName
        self.lastName = lastName
        self.gradeLevel = gradeLevel
        self.schoolLevel = gradeLevel.schoolLevel
        self.dateOfBirth = nil
        self.specialNeeds = specialNeeds
        self.learningPreferences = learningPreferences
        self.enrollmentDate = Date()
        self.parentEmail = parentEmail
        self.parentPhone = parentPhone
        self.isActive = true
        self.achievements = []
    }

    var fullName: String {
        return "\(firstName) \(lastName)"
    }

    var age: Int? {
        guard let dateOfBirth = dateOfBirth else { return nil }
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: now)
        return ageComponents.year
    }
}

// SubjectCategory is already defined above (line 93)

// MARK: - Subject Model

struct Subject: Identifiable, Codable {
    let id: UUID
    let name: String
    let code: String
    let category: SubjectCategory
    let schoolLevels: [SchoolLevel]
    let gradeLevels: [GradeLevel]
    let subjectDescription: String
    let isAP: Bool
    let prerequisites: [String]
    let isActive: Bool

    init(name: String, code: String, category: SubjectCategory, schoolLevels: [SchoolLevel], gradeLevels: [GradeLevel], description: String = "", isAP: Bool = false, prerequisites: [String] = []) {
        self.id = UUID()
        self.name = name
        self.code = code
        self.category = category
        self.schoolLevels = schoolLevels
        self.gradeLevels = gradeLevels
        self.subjectDescription = description
        self.isAP = isAP
        self.prerequisites = prerequisites
        self.isActive = true
    }

    func isAvailableFor(gradeLevel: GradeLevel) -> Bool {
        return gradeLevels.contains(gradeLevel)
    }

    func isAvailableFor(schoolLevel: SchoolLevel) -> Bool {
        return schoolLevels.contains(schoolLevel)
    }
}

// MARK: - Student-Teacher Assignment Model

@Model
class StudentTeacherAssignment {
    var id: UUID
    var studentId: UUID
    var teacherId: UUID
    var subjectId: UUID
    var assignedAt: Date
    var isActive: Bool

    init(studentId: UUID, teacherId: UUID, subjectId: UUID) {
        self.id = UUID()
        self.studentId = studentId
        self.teacherId = teacherId
        self.subjectId = subjectId
        self.assignedAt = Date()
        self.isActive = true
    }
}

// MARK: - Learning Session Model

@Model
class LearningSession {
    var id: UUID
    var studentId: UUID
    var teacherId: UUID
    var subjectId: UUID
    var sessionType: String
    var durationMinutes: Int
    var objectives: [String]
    var progressScore: Double?
    var emotionalState: String?
    var adaptationsUsed: [String]
    var startedAt: Date
    var completedAt: Date?
    var notes: String

    init(studentId: UUID, teacherId: UUID, subjectId: UUID, sessionType: String, objectives: [String] = []) {
        self.id = UUID()
        self.studentId = studentId
        self.teacherId = teacherId
        self.subjectId = subjectId
        self.sessionType = sessionType
        self.durationMinutes = 0
        self.objectives = objectives
        self.progressScore = nil
        self.emotionalState = nil
        self.adaptationsUsed = []
        self.startedAt = Date()
        self.completedAt = nil
        self.notes = ""
    }
}
