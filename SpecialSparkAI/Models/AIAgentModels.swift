//
//  AIAgentModels.swift
//  SpecialSparkAI
//
//  AI Agent Models for LangGraph and CrewAI Integration
//

import Foundation
import SwiftData

// MARK: - AI Agent Core Models

@Model
final class AIAgent {
    var id: UUID
    var name: String
    var agentType: AgentType
    var specialization: String
    var personality: AgentPersonality
    var capabilities: [AgentCapability]
    var currentState: AgentState
    var memoryContext: String // JSON string for agent memory
    var learningHistory: [LearningInteraction]
    var isActive: Bool
    var createdDate: Date
    var lastInteraction: Date

    // LangGraph specific properties
    var workflowState: String // Current workflow node
    var graphConfiguration: String // JSON configuration

    // CrewAI specific properties
    var role: String
    var goal: String
    var backstory: String
    var tools: [String] // Available tools for the agent

    init(name: String, agentType: AgentType, specialization: String) {
        self.id = UUID()
        self.name = name
        self.agentType = agentType
        self.specialization = specialization
        self.personality = AgentPersonality()
        self.capabilities = []
        self.currentState = .idle
        self.memoryContext = "{}"
        self.learningHistory = []
        self.isActive = true
        self.createdDate = Date()
        self.lastInteraction = Date()
        self.workflowState = "start"
        self.graphConfiguration = "{}"
        self.role = agentType.defaultRole
        self.goal = "Provide exceptional personalized education"
        self.backstory = "An AI teacher dedicated to helping every student succeed"
        self.tools = agentType.defaultTools
    }
}

enum AgentType: String, CaseIterable, Codable {
    case subjectSpecialist = "Subject Specialist"
    case learningCoach = "Learning Coach"
    case emotionalSupport = "Emotional Support"
    case assessmentAgent = "Assessment Agent"
    case parentCommunicator = "Parent Communicator"
    case adaptiveTutor = "Adaptive Tutor"
    case creativeMentor = "Creative Mentor"
    case socialSkillsCoach = "Social Skills Coach"

    var defaultRole: String {
        switch self {
        case .subjectSpecialist:
            return "Expert educator in specific academic subjects"
        case .learningCoach:
            return "Personalized learning guide and mentor"
        case .emotionalSupport:
            return "Empathetic supporter for emotional well-being"
        case .assessmentAgent:
            return "Adaptive assessment and progress evaluator"
        case .parentCommunicator:
            return "Bridge between student progress and parent understanding"
        case .adaptiveTutor:
            return "One-on-one adaptive learning facilitator"
        case .creativeMentor:
            return "Creative expression and artistic development guide"
        case .socialSkillsCoach:
            return "Social interaction and communication skills developer"
        }
    }

    var defaultTools: [String] {
        switch self {
        case .subjectSpecialist:
            return ["curriculum_access", "assessment_tools", "visual_aids", "interactive_simulations"]
        case .learningCoach:
            return ["progress_tracker", "goal_setter", "motivation_tools", "learning_analytics"]
        case .emotionalSupport:
            return ["emotion_detector", "calming_techniques", "confidence_builder", "stress_relief"]
        case .assessmentAgent:
            return ["adaptive_testing", "progress_analysis", "skill_mapping", "report_generator"]
        case .parentCommunicator:
            return ["progress_reporter", "communication_bridge", "insight_generator", "recommendation_engine"]
        case .adaptiveTutor:
            return ["personalization_engine", "difficulty_adjuster", "learning_path_optimizer", "real_time_feedback"]
        case .creativeMentor:
            return ["art_tools", "music_creator", "story_builder", "creative_prompts"]
        case .socialSkillsCoach:
            return ["social_scenarios", "communication_practice", "empathy_builder", "conflict_resolution"]
        }
    }
}

@Model
final class AgentPersonality {
    var id: UUID
    var warmth: Int // 1-10 scale
    var patience: Int // 1-10 scale
    var enthusiasm: Int // 1-10 scale
    var humor: Int // 1-10 scale
    var empathy: Int // 1-10 scale
    var adaptability: Int // 1-10 scale
    var communicationStyle: AgentCommunicationStyle
    var preferredInteractionMode: InteractionMode
    var specialNeedsAdaptations: [String]

    init() {
        self.id = UUID()
        self.warmth = 8
        self.patience = 9
        self.enthusiasm = 7
        self.humor = 6
        self.empathy = 9
        self.adaptability = 8
        self.communicationStyle = .adaptive
        self.preferredInteractionMode = .multimodal
        self.specialNeedsAdaptations = []
    }
}

enum AgentCommunicationStyle: String, CaseIterable, Codable {
    case formal = "Formal"
    case casual = "Casual"
    case playful = "Playful"
    case encouraging = "Encouraging"
    case adaptive = "Adaptive"
    case visual = "Visual-Heavy"
    case auditory = "Auditory-Focused"
    case kinesthetic = "Hands-On"
}

enum InteractionMode: String, CaseIterable, Codable {
    case text = "Text-Based"
    case voice = "Voice-Based"
    case visual = "Visual-Based"
    case multimodal = "Multimodal"
    case gamified = "Gamified"
    case immersive = "Immersive VR/AR"
}

enum AgentState: String, CaseIterable, Codable {
    case idle = "Idle"
    case teaching = "Teaching"
    case assessing = "Assessing"
    case planning = "Planning"
    case collaborating = "Collaborating"
    case reflecting = "Reflecting"
    case adapting = "Adapting"
    case communicating = "Communicating"
}

@Model
final class AgentCapability {
    var id: UUID
    var agentId: UUID
    var name: String
    var details: String
    var proficiencyLevel: Int // 1-10 scale
    var isActive: Bool
    var prerequisites: [String]
    var outcomes: [String]

    init(agentId: UUID, name: String, details: String) {
        self.id = UUID()
        self.agentId = agentId
        self.name = name
        self.details = details
        self.proficiencyLevel = 8
        self.isActive = true
        self.prerequisites = []
        self.outcomes = []
    }
}

@Model
final class LearningInteraction {
    var id: UUID
    var agentId: UUID
    var studentId: UUID
    var interactionType: InteractionType
    var content: String
    var context: String // JSON context data
    var outcome: InteractionOutcome
    var duration: Int // seconds
    var timestamp: Date
    var emotionalState: EmotionalState
    var learningObjectives: [String]
    var adaptationsUsed: [String]

    init(agentId: UUID, studentId: UUID, interactionType: InteractionType, content: String) {
        self.id = UUID()
        self.agentId = agentId
        self.studentId = studentId
        self.interactionType = interactionType
        self.content = content
        self.context = "{}"
        self.outcome = .pending
        self.duration = 0
        self.timestamp = Date()
        self.emotionalState = .neutral
        self.learningObjectives = []
        self.adaptationsUsed = []
    }
}

enum InteractionType: String, CaseIterable, Codable {
    case lesson = "Lesson"
    case assessment = "Assessment"
    case conversation = "Conversation"
    case guidance = "Guidance"
    case feedback = "Feedback"
    case encouragement = "Encouragement"
    case problemSolving = "Problem Solving"
    case creativitySession = "Creativity Session"
}

enum InteractionOutcome: String, CaseIterable, Codable {
    case pending = "Pending"
    case successful = "Successful"
    case needsImprovement = "Needs Improvement"
    case exceptional = "Exceptional"
    case requiresFollowUp = "Requires Follow-up"
    case adaptationNeeded = "Adaptation Needed"
}

enum EmotionalState: String, CaseIterable, Codable {
    case excited = "Excited"
    case happy = "Happy"
    case neutral = "Neutral"
    case confused = "Confused"
    case frustrated = "Frustrated"
    case anxious = "Anxious"
    case confident = "Confident"
    case curious = "Curious"
    case overwhelmed = "Overwhelmed"
    case proud = "Proud"
}

// MARK: - LangGraph Workflow Models

@Model
final class LearningWorkflow {
    var id: UUID
    var agentId: UUID
    var studentId: UUID
    var workflowType: WorkflowType
    var currentNode: String
    var nodeHistory: [String]
    var state: String // JSON state
    var isComplete: Bool
    var startTime: Date
    var endTime: Date?
    var objectives: [String]
    var adaptations: [String]

    init(agentId: UUID, studentId: UUID, workflowType: WorkflowType) {
        self.id = UUID()
        self.agentId = agentId
        self.studentId = studentId
        self.workflowType = workflowType
        self.currentNode = "start"
        self.nodeHistory = ["start"]
        self.state = "{}"
        self.isComplete = false
        self.startTime = Date()
        self.objectives = []
        self.adaptations = []
    }
}

enum WorkflowType: String, CaseIterable, Codable {
    case adaptiveLearning = "Adaptive Learning"
    case assessment = "Assessment"
    case personalizedTutoring = "Personalized Tutoring"
    case emotionalSupport = "Emotional Support"
    case creativeLearning = "Creative Learning"
    case socialSkills = "Social Skills"
    case parentCommunication = "Parent Communication"
}

// MARK: - CrewAI Collaboration Models

@Model
final class AgentCrew {
    var id: UUID
    var name: String
    var purpose: String
    var agents: [UUID] // Agent IDs
    var currentTask: String?
    var isActive: Bool
    var createdDate: Date
    var collaborationHistory: [CrewInteraction]

    init(name: String, purpose: String) {
        self.id = UUID()
        self.name = name
        self.purpose = purpose
        self.agents = []
        self.isActive = true
        self.createdDate = Date()
        self.collaborationHistory = []
    }
}

@Model
final class CrewInteraction {
    var id: UUID
    var crewId: UUID
    var initiatingAgentId: UUID
    var participatingAgents: [UUID]
    var task: String
    var outcome: String
    var timestamp: Date
    var duration: Int

    init(crewId: UUID, initiatingAgentId: UUID, task: String) {
        self.id = UUID()
        self.crewId = crewId
        self.initiatingAgentId = initiatingAgentId
        self.participatingAgents = []
        self.task = task
        self.outcome = ""
        self.timestamp = Date()
        self.duration = 0
    }
}

// MARK: - Supporting Types for AIAgentService

struct InteractionAnalysis {
    let needsMorePatience: Bool
    let needsMoreEnthusiasm: Bool
    let needsDifferentCommunicationStyle: Bool
    let recommendedStyle: AgentCommunicationStyle
    let recommendedAdaptations: [String]
    let overallEffectiveness: Double
}

struct LangGraphResponse {
    let prompt: String
    let context: String
    let nextNode: String
    let adaptations: [String]
}
