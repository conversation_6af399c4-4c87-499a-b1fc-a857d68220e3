//
//  SocialFeaturesModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Social Features Models

struct SocialProfile: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let displayName: String
    let avatar: String
    let bio: String
    let interests: [Interest]
    let achievements: [SocialAchievement]
    let privacySettings: PrivacySettings
    let safetySettings: SafetySettings
    let parentalControls: ParentalControls
    let isActive: Bool
    let createdAt: String
    let lastActive: String
}

struct Interest: Identifiable, Codable {
    let id: UUID
    let name: String
    let category: InterestCategory
    let level: InterestLevel
    let isPublic: Bool
}

enum InterestCategory: String, CaseIterable, Codable {
    case academic = "Academic"
    case creative = "Creative"
    case sports = "Sports"
    case technology = "Technology"
    case nature = "Nature"
    case music = "Music"
    case reading = "Reading"
    case games = "Games"
    case science = "Science"
    case arts = "Arts"
}

enum InterestLevel: String, CaseIterable, Codable {
    case beginner = "Beginner"
    case intermediate = "Intermediate"
    case advanced = "Advanced"
    case expert = "Expert"
}

struct SocialAchievement: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let icon: String
    let category: AchievementCategory
    let earnedDate: String
    let isVisible: Bool
    let shareCount: Int
}

struct PrivacySettings: Codable {
    let profileVisibility: ProfileVisibility
    let achievementVisibility: AchievementVisibility
    let activityVisibility: ActivityVisibility
    let contactPermissions: ContactPermissions
    let dataSharing: DataSharingSettings
}

enum ProfileVisibility: String, CaseIterable, Codable {
    case publicProfile = "Public"
    case friends = "Friends Only"
    case classmates = "Classmates Only"
    case privateProfile = "Private"
}

enum AchievementVisibility: String, CaseIterable, Codable {
    case all = "All Achievements"
    case selected = "Selected Only"
    case none = "None"
}

enum ActivityVisibility: String, CaseIterable, Codable {
    case all = "All Activities"
    case learning = "Learning Only"
    case social = "Social Only"
    case none = "None"
}

struct ContactPermissions: Codable {
    let allowFriendRequests: Bool
    let allowMessages: Bool
    let allowGroupInvites: Bool
    let allowMentoring: Bool
    let restrictedContacts: [UUID]
}

struct DataSharingSettings: Codable {
    let shareProgress: Bool
    let shareAchievements: Bool
    let shareInterests: Bool
    let shareProjects: Bool
    let anonymousAnalytics: Bool
}

struct SafetySettings: Codable {
    let contentFiltering: ContentFiltering
    let communicationMonitoring: CommunicationMonitoring
    let reportingSettings: ReportingSettings
    let emergencyContacts: [EmergencyContact]
}

struct ContentFiltering: Codable {
    let level: FilteringLevel
    let customFilters: [ContentFilter]
    let blockedKeywords: [String]
    let allowedDomains: [String]
}

enum FilteringLevel: String, CaseIterable, Codable {
    case strict = "Strict"
    case moderate = "Moderate"
    case basic = "Basic"
    case custom = "Custom"
}

struct ContentFilter: Identifiable, Codable {
    let id: UUID
    let type: FilterType
    let criteria: String
    let action: FilterAction
    let isActive: Bool
}

struct CommunicationMonitoring: Codable {
    let monitorMessages: Bool
    let monitorVoiceChat: Bool
    let monitorVideoChat: Bool
    let flagInappropriate: Bool
    let notifyParents: Bool
    let logCommunications: Bool
}

struct ReportingSettings: Codable {
    let enableReporting: Bool
    let anonymousReporting: Bool
    let reportCategories: [ReportCategory]
    let escalationRules: [EscalationRule]
}

enum ReportCategory: String, CaseIterable, Codable {
    case bullying = "Bullying"
    case inappropriate = "Inappropriate Content"
    case harassment = "Harassment"
    case spam = "Spam"
    case safety = "Safety Concern"
    case technical = "Technical Issue"
    case other = "Other"
}

struct EscalationRule: Identifiable, Codable {
    let id: UUID
    let condition: String
    let action: EscalationAction
    let timeframe: Int // minutes
}

enum EscalationAction: String, CaseIterable, Codable {
    case notifyModerator = "Notify Moderator"
    case notifyParent = "Notify Parent"
    case suspendAccount = "Suspend Account"
    case blockUser = "Block User"
    case flagContent = "Flag Content"
}

struct EmergencyContact: Identifiable, Codable {
    let id: UUID
    let name: String
    let relationship: String
    let phone: String
    let email: String
    let isPrimary: Bool
}

struct ParentalControls: Codable {
    let timeRestrictions: TimeRestrictions
    let featureRestrictions: FeatureRestrictions
    let approvalRequired: ApprovalSettings
    let monitoringLevel: MonitoringLevel
}

struct TimeRestrictions: Codable {
    let dailyLimit: Int // minutes
    let weeklyLimit: Int // minutes
    let allowedHours: [TimeWindow]
    let breakReminders: Bool
    let bedtimeMode: BedtimeMode
}

struct TimeWindow: Identifiable, Codable {
    let id: UUID
    let startTime: String
    let endTime: String
    let weekdays: [Int] // 0-6, Sunday = 0
}

struct BedtimeMode: Codable {
    let enabled: Bool
    let startTime: String
    let endTime: String
    let weekdays: [Int]
    let allowEmergency: Bool
}

struct FeatureRestrictions: Codable {
    let allowMessaging: Bool
    let allowVoiceChat: Bool
    let allowVideoChat: Bool
    let allowGroupCreation: Bool
    let allowFileSharing: Bool
    let allowExternalLinks: Bool
    let restrictedFeatures: [String]
}

struct ApprovalSettings: Codable {
    let friendRequests: Bool
    let groupJoining: Bool
    let contentSharing: Bool
    let profileChanges: Bool
    let appDownloads: Bool
}

enum MonitoringLevel: String, CaseIterable, Codable {
    case high = "High"
    case medium = "Medium"
    case low = "Low"
    case none = "None"
}

// MARK: - Social Interactions

struct Friendship: Identifiable, Codable {
    let id: UUID
    let requester: UUID
    let recipient: UUID
    let status: FriendshipStatus
    let requestDate: String
    let acceptedDate: String?
    let sharedActivities: [SharedActivity]
    let interactionHistory: [SocialInteraction]
    let mutualFriends: [UUID]
}

enum FriendshipStatus: String, CaseIterable, Codable {
    case pending = "Pending"
    case accepted = "Accepted"
    case blocked = "Blocked"
    case declined = "Declined"
}

struct SharedActivity: Identifiable, Codable {
    let id: UUID
    let type: ActivityType
    let name: String
    let participants: [UUID]
    let startTime: String
    let endTime: String?
    let outcome: ActivityOutcome?
}

// ActivityType is defined in AITeacherModels.swift to avoid conflicts

struct ActivityOutcome: Codable {
    let completed: Bool
    let achievements: [UUID]
    let feedback: [ParticipantFeedback]
    let rating: Double
}

struct ParticipantFeedback: Identifiable, Codable {
    let id: UUID
    let participantId: UUID
    let rating: Int // 1-5
    let comment: String?
    let skills: [SkillRating]
}

struct SkillRating: Identifiable, Codable {
    let id: UUID
    let skill: SocialSkill
    let rating: Int // 1-5
}

enum SocialSkill: String, CaseIterable, Codable {
    case communication = "Communication"
    case collaboration = "Collaboration"
    case leadership = "Leadership"
    case empathy = "Empathy"
    case problemSolving = "Problem Solving"
    case creativity = "Creativity"
    case patience = "Patience"
    case helpfulness = "Helpfulness"
}

struct SocialInteraction: Identifiable, Codable {
    let id: UUID
    let type: InteractionType
    let timestamp: String
    let duration: Int? // seconds
    let context: String
    let sentiment: SentimentAnalysis?
    let outcome: InteractionOutcome
}

enum InteractionType: String, CaseIterable, Codable {
    case message = "Message"
    case voiceCall = "Voice Call"
    case videoCall = "Video Call"
    case collaboration = "Collaboration"
    case game = "Game"
    case help = "Help Request"
    case celebration = "Celebration"
}

struct SentimentAnalysis: Codable {
    let overall: Sentiment
    let confidence: Double
    let emotions: [EmotionScore]
    let topics: [String]
}

enum Sentiment: String, CaseIterable, Codable {
    case positive = "Positive"
    case neutral = "Neutral"
    case negative = "Negative"
    case mixed = "Mixed"
}

struct EmotionScore: Identifiable, Codable {
    let id: UUID
    let emotion: Emotion
    let score: Double // 0.0 to 1.0
}

enum Emotion: String, CaseIterable, Codable {
    case joy = "Joy"
    case excitement = "Excitement"
    case pride = "Pride"
    case frustration = "Frustration"
    case confusion = "Confusion"
    case curiosity = "Curiosity"
    case empathy = "Empathy"
    case encouragement = "Encouragement"
}

enum InteractionOutcome: String, CaseIterable, Codable {
    case positive = "Positive"
    case neutral = "Neutral"
    case negative = "Negative"
    case flagged = "Flagged"
    case escalated = "Escalated"
}

// MARK: - Social Groups

struct SocialGroup: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let type: GroupType
    let category: GroupCategory
    let creator: UUID
    let moderators: [UUID]
    let members: [GroupMember]
    let settings: GroupSettings
    let activities: [GroupActivity]
    let rules: [GroupRule]
    let isActive: Bool
    let createdAt: String
}

enum GroupType: String, CaseIterable, Codable {
    case study = "Study Group"
    case project = "Project Team"
    case interest = "Interest Group"
    case support = "Support Group"
    case classGroup = "Class Group"
    case club = "Club"
    case mentorship = "Mentorship Circle"
}

enum GroupCategory: String, CaseIterable, Codable {
    case academic = "Academic"
    case social = "Social"
    case creative = "Creative"
    case support = "Support"
    case recreational = "Recreational"
}

struct GroupMember: Identifiable, Codable {
    let id: UUID
    let studentId: UUID
    let role: GroupRole
    let joinedDate: String
    let contributionScore: Double
    let participationLevel: ParticipationLevel
    let permissions: [GroupPermission]
}

enum GroupRole: String, CaseIterable, Codable {
    case owner = "Owner"
    case moderator = "Moderator"
    case member = "Member"
    case guest = "Guest"
}

enum ParticipationLevel: String, CaseIterable, Codable {
    case high = "High"
    case medium = "Medium"
    case low = "Low"
    case inactive = "Inactive"
}

enum GroupPermission: String, CaseIterable, Codable {
    case invite = "Invite Members"
    case moderate = "Moderate Content"
    case schedule = "Schedule Activities"
    case share = "Share Files"
    case admin = "Admin Functions"
}

struct GroupSettings: Codable {
    let visibility: GroupVisibility
    let joinPolicy: JoinPolicy
    let contentModeration: ContentModeration
    let activityNotifications: Bool
    let memberLimit: Int?
    let ageRestrictions: AgeRestrictions?
}

enum GroupVisibility: String, CaseIterable, Codable {
    case publicGroup = "Public"
    case privateGroup = "Private"
    case hidden = "Hidden"
    case inviteOnly = "Invite Only"
}

enum JoinPolicy: String, CaseIterable, Codable {
    case open = "Open"
    case approval = "Approval Required"
    case invitation = "Invitation Only"
    case closed = "Closed"
}

struct ContentModeration: Codable {
    let autoModeration: Bool
    let humanModeration: Bool
    let preApproval: Bool
    let reportingEnabled: Bool
    let moderationLevel: ModerationLevel
}

enum ModerationLevel: String, CaseIterable, Codable {
    case strict = "Strict"
    case moderate = "Moderate"
    case relaxed = "Relaxed"
}

struct AgeRestrictions: Codable {
    let minimumAge: Int
    let maximumAge: Int
    let gradeRestrictions: [String]
}

struct GroupActivity: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: ActivityType
    let scheduledTime: String?
    let duration: Int? // minutes
    let participants: [UUID]
    let resources: [ActivityResource]
    let status: ActivityStatus
    let outcomes: [ActivityOutcome]
}

struct ActivityResource: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: ResourceType
    let url: String?
    let description: String
    let isRequired: Bool
}

struct GroupRule: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let category: RuleCategory
    let severity: RuleSeverity
    let consequences: [RuleConsequence]
    let isActive: Bool
}

enum RuleCategory: String, CaseIterable, Codable {
    case behavior = "Behavior"
    case communication = "Communication"
    case content = "Content"
    case participation = "Participation"
    case safety = "Safety"
}

enum RuleSeverity: String, CaseIterable, Codable {
    case minor = "Minor"
    case moderate = "Moderate"
    case major = "Major"
    case severe = "Severe"
}

struct RuleConsequence: Identifiable, Codable {
    let id: UUID
    let action: ConsequenceAction
    let duration: Int? // minutes/hours/days
    let description: String
}

enum ConsequenceAction: String, CaseIterable, Codable {
    case warning = "Warning"
    case timeout = "Timeout"
    case suspension = "Suspension"
    case removal = "Removal"
    case restriction = "Feature Restriction"
}

// MARK: - Social Analytics

struct SocialAnalytics: Codable {
    let studentId: UUID
    let socialScore: Double
    let friendshipMetrics: FriendshipMetrics
    let communicationMetrics: CommunicationMetrics
    let collaborationMetrics: CollaborationMetrics
    let leadershipMetrics: LeadershipMetrics
    let empathyMetrics: EmpathyMetrics
    let growthIndicators: [GrowthIndicator]
    let recommendations: [SocialRecommendation]
}

struct FriendshipMetrics: Codable {
    let totalFriends: Int
    let activeFriendships: Int
    let friendshipQuality: Double
    let mutualConnections: Int
    let socialCircleSize: Int
}

struct CommunicationMetrics: Codable {
    let messagesExchanged: Int
    let averageResponseTime: Int // minutes
    let communicationClarity: Double
    let positivityScore: Double
    let helpfulnessRating: Double
}

struct CollaborationMetrics: Codable {
    let projectsCompleted: Int
    let collaborationSuccess: Double
    let teamworkRating: Double
    let contributionLevel: Double
    let conflictResolution: Double
}

struct LeadershipMetrics: Codable {
    let leadershipOpportunities: Int
    let leadershipEffectiveness: Double
    let mentorshipProvided: Int
    let initiativesTaken: Int
    let influenceScore: Double
}

struct EmpathyMetrics: Codable {
    let empathyScore: Double
    let supportProvided: Int
    let emotionalIntelligence: Double
    let kindnessRating: Double
    let inclusivityScore: Double
}

struct GrowthIndicator: Identifiable, Codable {
    let id: UUID
    let skill: SocialSkill
    let currentLevel: Double
    let growthRate: Double
    let trend: TrendDirection
    let timeframe: String
}

struct SocialRecommendation: Identifiable, Codable {
    let id: UUID
    let type: RecommendationType
    let title: String
    let description: String
    let actionItems: [String]
    let priority: RecommendationPriority
    let estimatedImpact: Double
}
