//
//  Models.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftData

// MARK: - Student Models

@Model
final class Student {
    var id: UUID
    var firstName: String
    var lastName: String
    var dateOfBirth: Date
    var gradeLevel: Int
    var profileImage: String?
    var learningProfile: LearningProfile?
    var specialNeeds: [SpecialNeed]
    var achievements: [Achievement]
    var enrollmentDate: Date
    var isActive: Bool

    init(firstName: String, lastName: String, dateOfBirth: Date, gradeLevel: Int) {
        self.id = UUID()
        self.firstName = firstName
        self.lastName = lastName
        self.dateOfBirth = dateOfBirth
        self.gradeLevel = gradeLevel
        self.specialNeeds = []
        self.achievements = []
        self.enrollmentDate = Date()
        self.isActive = true
    }

    var fullName: String {
        "\(firstName) \(lastName)"
    }

    var age: Int {
        Calendar.current.dateComponents([.year], from: dateOfBirth, to: Date()).year ?? 0
    }
}

@Model
final class LearningProfile {
    var id: UUID
    var studentId: UUID
    var learningStyle: LearningStyle
    var preferredPace: LearningPace
    var strengths: [String]
    var challenges: [String]
    var interests: [String]
    var accommodations: [String]
    var lastUpdated: Date

    init(studentId: UUID, learningStyle: LearningStyle = .visual, preferredPace: LearningPace = .moderate) {
        self.id = UUID()
        self.studentId = studentId
        self.learningStyle = learningStyle
        self.preferredPace = preferredPace
        self.strengths = []
        self.challenges = []
        self.interests = []
        self.accommodations = []
        self.lastUpdated = Date()
    }
}

enum LearningStyle: String, CaseIterable, Codable {
    case visual = "Visual"
    case auditory = "Auditory"
    case kinesthetic = "Kinesthetic"
    case readingWriting = "Reading/Writing"
    case multimodal = "Multimodal"
}

enum LearningPace: String, CaseIterable, Codable {
    case slow = "Slow"
    case moderate = "Moderate"
    case fast = "Fast"
    case variable = "Variable"
}

@Model
final class SpecialNeed {
    var id: UUID
    var studentId: UUID
    var category: SpecialNeedCategory
    var details: String
    var accommodations: [String]
    var supportStrategies: [String]
    var isActive: Bool
    var dateIdentified: Date

    init(studentId: UUID, category: SpecialNeedCategory, details: String) {
        self.id = UUID()
        self.studentId = studentId
        self.category = category
        self.details = details
        self.accommodations = []
        self.supportStrategies = []
        self.isActive = true
        self.dateIdentified = Date()
    }
}

enum SpecialNeedCategory: String, CaseIterable, Codable {
    case autism = "Autism Spectrum"
    case adhd = "ADHD"
    case learningDisability = "Learning Disability"
    case intellectualDisability = "Intellectual Disability"
    case visualImpairment = "Visual Impairment"
    case hearingImpairment = "Hearing Impairment"
    case physicalDisability = "Physical Disability"
    case speechLanguage = "Speech/Language"
    case emotionalBehavioral = "Emotional/Behavioral"
    case giftedTalented = "Gifted/Talented"
    case other = "Other"
}

@Model
final class Achievement {
    var id: UUID
    var studentId: UUID
    var title: String
    var details: String
    var category: AchievementCategory
    var dateEarned: Date
    var points: Int
    var badgeImage: String?

    init(studentId: UUID, title: String, details: String, category: AchievementCategory, points: Int = 10) {
        self.id = UUID()
        self.studentId = studentId
        self.title = title
        self.details = details
        self.category = category
        self.dateEarned = Date()
        self.points = points
    }
}

enum AchievementCategory: String, CaseIterable, Codable {
    case academic = "Academic"
    case social = "Social"
    case creative = "Creative"
    case leadership = "Leadership"
    case perseverance = "Perseverance"
    case kindness = "Kindness"
    case innovation = "Innovation"
    case collaboration = "Collaboration"
}
