//
//  AITeacherModels.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftData

// MARK: - AI Teacher Models

@Model
final class AITeacher {
    var id: UUID
    var name: String
    var subject: Subject
    var personality: TeacherPersonality?
    var specializations: [String]
    var experienceLevel: ExperienceLevel
    var avatar: String?
    var voiceProfile: VoiceProfile
    var teachingMethods: [TeachingMethod]
    var isActive: Bool
    var createdDate: Date

    init(name: String, subject: Subject, personality: TeacherPersonality? = nil) {
        self.id = UUID()
        self.name = name
        self.subject = subject
        self.personality = personality
        self.specializations = []
        self.experienceLevel = .experienced
        self.voiceProfile = VoiceProfile()
        self.teachingMethods = []
        self.isActive = true
        self.createdDate = Date()
    }
}

enum TeacherSubject: String, CaseIterable, Codable {
    case mathematics = "Mathematics"
    case english = "English Language Arts"
    case science = "Science"
    case socialStudies = "Social Studies"
    case art = "Art"
    case music = "Music"
    case physicalEducation = "Physical Education"
    case computerScience = "Computer Science"
    case foreignLanguage = "Foreign Language"
    case specialEducation = "Special Education"
    case counseling = "Counseling"
    case libraryScience = "Library Science"
}

@Model
final class TeacherPersonality {
    var id: UUID
    var teacherId: UUID
    var warmth: Int // 1-10 scale
    var patience: Int // 1-10 scale
    var enthusiasm: Int // 1-10 scale
    var humor: Int // 1-10 scale
    var strictness: Int // 1-10 scale
    var creativity: Int // 1-10 scale
    var empathy: Int // 1-10 scale
    var adaptability: Int // 1-10 scale
    var communicationStyle: CommunicationStyle
    var motivationStyle: MotivationStyle

    init(teacherId: UUID) {
        self.id = UUID()
        self.teacherId = teacherId
        self.warmth = 8
        self.patience = 9
        self.enthusiasm = 7
        self.humor = 6
        self.strictness = 5
        self.creativity = 8
        self.empathy = 9
        self.adaptability = 8
        self.communicationStyle = .encouraging
        self.motivationStyle = .positive
    }
}

// CommunicationStyle is defined in EnhancedAIPersonalities.swift to avoid conflicts

enum MotivationStyle: String, CaseIterable, Codable {
    case positive = "Positive Reinforcement"
    case challenge = "Challenge-Based"
    case collaborative = "Collaborative"
    case achievement = "Achievement-Focused"
    case intrinsic = "Intrinsic Motivation"
}

enum ExperienceLevel: String, CaseIterable, Codable {
    case novice = "Novice"
    case developing = "Developing"
    case experienced = "Experienced"
    case expert = "Expert"
    case master = "Master"
}

@Model
final class VoiceProfile {
    var id: UUID
    var teacherId: UUID
    var voiceType: VoiceType
    var speed: VoiceSpeed
    var pitch: VoicePitch
    var accent: VoiceAccent
    var emotionalRange: Int // 1-10 scale

    init(teacherId: UUID = UUID()) {
        self.id = UUID()
        self.teacherId = teacherId
        self.voiceType = .warm
        self.speed = .moderate
        self.pitch = .medium
        self.accent = .neutral
        self.emotionalRange = 7
    }
}

enum VoiceType: String, CaseIterable, Codable {
    case warm = "Warm"
    case energetic = "Energetic"
    case calm = "Calm"
    case authoritative = "Authoritative"
    case friendly = "Friendly"
    case gentle = "Gentle"
}

enum VoiceSpeed: String, CaseIterable, Codable {
    case slow = "Slow"
    case moderate = "Moderate"
    case fast = "Fast"
    case variable = "Variable"
}

enum VoicePitch: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case variable = "Variable"
}

enum VoiceAccent: String, CaseIterable, Codable {
    case neutral = "Neutral"
    case american = "American"
    case british = "British"
    case australian = "Australian"
    case canadian = "Canadian"
}

@Model
final class TeachingMethod {
    var id: UUID
    var teacherId: UUID
    var name: String
    var details: String
    var effectiveness: Int // 1-10 scale
    var applicableGrades: [Int]
    var specialNeedsFriendly: Bool
    var requiredResources: [String]

    init(teacherId: UUID, name: String, details: String) {
        self.id = UUID()
        self.teacherId = teacherId
        self.name = name
        self.details = details
        self.effectiveness = 7
        self.applicableGrades = []
        self.specialNeedsFriendly = true
        self.requiredResources = []
    }
}

// MARK: - Lesson and Curriculum Models

@Model
final class Lesson {
    var id: UUID
    var teacherId: UUID
    var subject: Subject
    var title: String
    var details: String
    var gradeLevel: Int
    var duration: Int // minutes
    var objectives: [String]
    var activities: [LessonActivity]
    var assessments: [Assessment]
    var accommodations: [String]
    var resources: [String]
    var createdDate: Date
    var lastModified: Date

    init(teacherId: UUID, subject: Subject, title: String, gradeLevel: Int, duration: Int = 45) {
        self.id = UUID()
        self.teacherId = teacherId
        self.subject = subject
        self.title = title
        self.details = ""
        self.gradeLevel = gradeLevel
        self.duration = duration
        self.objectives = []
        self.activities = []
        self.assessments = []
        self.accommodations = []
        self.resources = []
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

@Model
final class LessonActivity {
    var id: UUID
    var lessonId: UUID
    var name: String
    var type: ActivityType
    var details: String
    var duration: Int // minutes
    var interactionLevel: InteractionLevel
    var difficultyLevel: Int // 1-10 scale
    var isAdaptive: Bool

    init(lessonId: UUID, name: String, type: ActivityType, duration: Int) {
        self.id = UUID()
        self.lessonId = lessonId
        self.name = name
        self.type = type
        self.details = ""
        self.duration = duration
        self.interactionLevel = .moderate
        self.difficultyLevel = 5
        self.isAdaptive = true
    }
}

enum ActivityType: String, CaseIterable, Codable {
    case lecture = "Lecture"
    case discussion = "Discussion"
    case handson = "Hands-on"
    case game = "Game"
    case quiz = "Quiz"
    case project = "Project"
    case experiment = "Experiment"
    case reading = "Reading"
    case writing = "Writing"
    case presentation = "Presentation"
}

enum InteractionLevel: String, CaseIterable, Codable {
    case low = "Low"
    case moderate = "Moderate"
    case high = "High"
    case collaborative = "Collaborative"
}

// Assessment is defined in AssessmentEngineModels.swift to avoid conflicts

// AssessmentType is defined in AssessmentEngineModels.swift to avoid conflicts

// AssessmentQuestion and QuestionType are defined in AssessmentEngineModels.swift to avoid conflicts
