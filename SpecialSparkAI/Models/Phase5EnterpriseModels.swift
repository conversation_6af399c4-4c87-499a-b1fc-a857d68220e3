//
//  Phase5EnterpriseModels.swift
//  SpecialSparkAI
//
//  Phase 5: Enterprise & Scaling Models
//  - Multi-tenant models
//  - Security models
//  - Performance models
//  - Deployment models
//  - Integration models
//

import Foundation
import SwiftUI

// MARK: - Multi-Tenant Models

struct Tenant: Identifiable, Codable {
    let id: UUID
    let name: String
    let organizationId: UUID
    let subscriptionTier: SubscriptionTier
    let isActive: Bool
    let createdAt: Date
    let settings: TenantSettings
    let limits: TenantLimits

    init(id: UUID, name: String, organizationId: UUID, subscriptionTier: SubscriptionTier, isActive: Bool) {
        self.id = id
        self.name = name
        self.organizationId = organizationId
        self.subscriptionTier = subscriptionTier
        self.isActive = isActive
        self.createdAt = Date()
        self.settings = TenantSettings.defaultSettings
        self.limits = subscriptionTier.limits
    }
}

enum SubscriptionTier: String, CaseIterable, Codable {
    case basic = "Basic"
    case professional = "Professional"
    case enterprise = "Enterprise"
    case custom = "Custom"

    var features: EnterpriseFeatures {
        switch self {
        case .basic:
            return EnterpriseFeatures.basicFeatures
        case .professional:
            return EnterpriseFeatures.professionalFeatures
        case .enterprise, .custom:
            return EnterpriseFeatures.allFeatures
        }
    }

    var limits: TenantLimits {
        switch self {
        case .basic:
            return TenantLimits.basicLimits
        case .professional:
            return TenantLimits.professionalLimits
        case .enterprise, .custom:
            return TenantLimits.enterpriseLimits
        }
    }
}

struct TenantConfiguration: Codable {
    let tenant: Tenant
    let features: EnterpriseFeatures
    let limits: TenantLimits
    let customizations: TenantCustomizations
    let integrations: [IntegrationType]

    init(tenant: Tenant, features: EnterpriseFeatures, limits: TenantLimits, customizations: TenantCustomizations) {
        self.tenant = tenant
        self.features = features
        self.limits = limits
        self.customizations = customizations
        self.integrations = []
    }
}

struct TenantSettings: Codable {
    let timeZone: String
    let locale: String
    let currency: String
    let dateFormat: String
    let theme: String
    let branding: BrandingSettings

    static let defaultSettings = TenantSettings(
        timeZone: "UTC",
        locale: "en_US",
        currency: "USD",
        dateFormat: "MM/dd/yyyy",
        theme: "default",
        branding: BrandingSettings.defaultBranding
    )
}

struct BrandingSettings: Codable {
    let logoURL: String?
    let primaryColor: String
    let secondaryColor: String
    let fontFamily: String
    let customCSS: String?

    static let defaultBranding = BrandingSettings(
        logoURL: nil,
        primaryColor: "#007AFF",
        secondaryColor: "#5856D6",
        fontFamily: "SF Pro",
        customCSS: nil
    )
}

struct TenantLimits: Codable {
    let maxUsers: Int
    let maxStorage: Double // GB
    let maxBandwidth: Double // GB/month
    let maxAPICallsPerMonth: Int
    let maxConcurrentSessions: Int
    let maxCustomReports: Int

    static let basicLimits = TenantLimits(
        maxUsers: 100,
        maxStorage: 10.0,
        maxBandwidth: 100.0,
        maxAPICallsPerMonth: 10000,
        maxConcurrentSessions: 50,
        maxCustomReports: 5
    )

    static let professionalLimits = TenantLimits(
        maxUsers: 500,
        maxStorage: 100.0,
        maxBandwidth: 1000.0,
        maxAPICallsPerMonth: 100000,
        maxConcurrentSessions: 250,
        maxCustomReports: 25
    )

    static let enterpriseLimits = TenantLimits(
        maxUsers: -1, // Unlimited
        maxStorage: -1.0, // Unlimited
        maxBandwidth: -1.0, // Unlimited
        maxAPICallsPerMonth: -1, // Unlimited
        maxConcurrentSessions: -1, // Unlimited
        maxCustomReports: -1 // Unlimited
    )
}

struct EnterpriseFeatures: Codable {
    let ssoIntegration: Bool
    let multiFactorAuth: Bool
    let advancedAnalytics: Bool
    let customReporting: Bool
    let apiAccess: Bool
    let webhooks: Bool
    let auditLogging: Bool
    let dataExport: Bool
    let whiteLabeling: Bool
    let prioritySupport: Bool
    let customIntegrations: Bool
    let advancedSecurity: Bool

    static let basicFeatures = EnterpriseFeatures(
        ssoIntegration: false,
        multiFactorAuth: false,
        advancedAnalytics: false,
        customReporting: false,
        apiAccess: false,
        webhooks: false,
        auditLogging: false,
        dataExport: true,
        whiteLabeling: false,
        prioritySupport: false,
        customIntegrations: false,
        advancedSecurity: false
    )

    static let professionalFeatures = EnterpriseFeatures(
        ssoIntegration: true,
        multiFactorAuth: true,
        advancedAnalytics: true,
        customReporting: true,
        apiAccess: true,
        webhooks: false,
        auditLogging: true,
        dataExport: true,
        whiteLabeling: false,
        prioritySupport: true,
        customIntegrations: false,
        advancedSecurity: true
    )

    static let allFeatures = EnterpriseFeatures(
        ssoIntegration: true,
        multiFactorAuth: true,
        advancedAnalytics: true,
        customReporting: true,
        apiAccess: true,
        webhooks: true,
        auditLogging: true,
        dataExport: true,
        whiteLabeling: true,
        prioritySupport: true,
        customIntegrations: true,
        advancedSecurity: true
    )
}

struct TenantCustomizations: Codable {
    let customFields: [CustomField]
    let workflowRules: [WorkflowRule]
    let notificationSettings: NotificationSettings
    let reportTemplates: [ReportTemplate]

    static let defaultCustomizations = TenantCustomizations(
        customFields: [],
        workflowRules: [],
        notificationSettings: NotificationSettings.defaultSettings,
        reportTemplates: []
    )
}

struct CustomField: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: FieldType
    let isRequired: Bool
    let defaultValue: String?
    let validationRules: [ValidationRule]

    enum FieldType: String, CaseIterable, Codable {
        case text = "Text"
        case number = "Number"
        case date = "Date"
        case boolean = "Boolean"
        case dropdown = "Dropdown"
        case multiSelect = "Multi-Select"
    }
}

struct ValidationRule: Codable {
    let type: ValidationType
    let value: String
    let errorMessage: String

    enum ValidationType: String, CaseIterable, Codable {
        case required = "Required"
        case minLength = "Min Length"
        case maxLength = "Max Length"
        case pattern = "Pattern"
        case range = "Range"
    }
}

struct WorkflowRule: Identifiable, Codable {
    let id: UUID
    let name: String
    let trigger: WorkflowTrigger
    let conditions: [WorkflowCondition]
    let actions: [WorkflowAction]
    let isActive: Bool
}

enum WorkflowTrigger: String, CaseIterable, Codable {
    case userCreated = "User Created"
    case userUpdated = "User Updated"
    case assessmentCompleted = "Assessment Completed"
    case reportGenerated = "Report Generated"
    case thresholdReached = "Threshold Reached"
}

struct WorkflowCondition: Codable {
    let field: String
    let `operator`: ConditionOperator
    let value: String

    enum ConditionOperator: String, CaseIterable, Codable {
        case equals = "Equals"
        case notEquals = "Not Equals"
        case greaterThan = "Greater Than"
        case lessThan = "Less Than"
        case contains = "Contains"
        case startsWith = "Starts With"
    }
}

struct WorkflowAction: Codable {
    let type: ActionType
    let parameters: [String: String]

    enum ActionType: String, CaseIterable, Codable {
        case sendEmail = "Send Email"
        case createTask = "Create Task"
        case updateField = "Update Field"
        case callWebhook = "Call Webhook"
        case generateReport = "Generate Report"
    }
}

struct NotificationSettings: Codable {
    let emailNotifications: Bool
    let pushNotifications: Bool
    let smsNotifications: Bool
    let webhookNotifications: Bool
    let notificationFrequency: NotificationFrequency

    static let defaultSettings = NotificationSettings(
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: false,
        webhookNotifications: false,
        notificationFrequency: .immediate
    )

    enum NotificationFrequency: String, CaseIterable, Codable {
        case immediate = "Immediate"
        case hourly = "Hourly"
        case daily = "Daily"
        case weekly = "Weekly"
        case never = "Never"
    }
}

struct ReportTemplate: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let type: ReportType
    let fields: [ReportField]
    let filters: [ReportFilter]
    let schedule: ReportSchedule?

    enum ReportType: String, CaseIterable, Codable {
        case student = "Student Report"
        case teacher = "Teacher Report"
        case classroom = "Classroom Report"
        case school = "School Report"
        case custom = "Custom Report"
    }
}

struct ReportField: Codable {
    let name: String
    let label: String
    let type: FieldType
    let aggregation: AggregationType?

    enum FieldType: String, CaseIterable, Codable {
        case string = "String"
        case number = "Number"
        case date = "Date"
        case boolean = "Boolean"
    }

    enum AggregationType: String, CaseIterable, Codable {
        case sum = "Sum"
        case average = "Average"
        case count = "Count"
        case min = "Minimum"
        case max = "Maximum"
    }
}

struct ReportFilter: Codable {
    let field: String
    let `operator`: FilterOperator
    let value: String

    enum FilterOperator: String, CaseIterable, Codable {
        case equals = "Equals"
        case notEquals = "Not Equals"
        case greaterThan = "Greater Than"
        case lessThan = "Less Than"
        case between = "Between"
        case `in` = "In"
        case notIn = "Not In"
    }
}

struct ReportSchedule: Codable {
    let frequency: ScheduleFrequency
    let time: String // HH:mm format
    let dayOfWeek: Int? // 1-7, Sunday = 1
    let dayOfMonth: Int? // 1-31
    let recipients: [String] // Email addresses

    enum ScheduleFrequency: String, CaseIterable, Codable {
        case daily = "Daily"
        case weekly = "Weekly"
        case monthly = "Monthly"
        case quarterly = "Quarterly"
    }
}

struct TenantAnalytics: Identifiable, Codable {
    let id = UUID()
    let tenantId: UUID
    let totalUsers: Int
    let activeUsers: Int
    let storageUsed: Double // TB
    let bandwidthUsed: Double // GB
    let apiCalls: Int
    let uptime: Double // Percentage
    let performance: Double // Score 0-1
    let generatedAt: Date

    init(tenantId: UUID, totalUsers: Int, activeUsers: Int, storageUsed: Double, bandwidthUsed: Double, apiCalls: Int, uptime: Double, performance: Double) {
        self.tenantId = tenantId
        self.totalUsers = totalUsers
        self.activeUsers = activeUsers
        self.storageUsed = storageUsed
        self.bandwidthUsed = bandwidthUsed
        self.apiCalls = apiCalls
        self.uptime = uptime
        self.performance = performance
        self.generatedAt = Date()
    }
}

struct Organization: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: OrganizationType
    let subscriptionTier: SubscriptionTier
    let contactEmail: String
    let contactPhone: String
    let address: Address
    let isActive: Bool
    let createdAt: Date

    enum OrganizationType: String, CaseIterable, Codable {
        case school = "School"
        case district = "School District"
        case university = "University"
        case corporation = "Corporation"
        case nonprofit = "Non-Profit"
        case government = "Government"
    }
}

struct Address: Codable {
    let street: String
    let city: String
    let state: String
    let zipCode: String
    let country: String
}

// MARK: - Security Models

enum SecurityStatus: String, CaseIterable, Codable {
    case secure = "Secure"
    case warning = "Warning"
    case critical = "Critical"
    case maintenance = "Maintenance"
}

enum SSOProvider: String, CaseIterable, Codable {
    case activeDirectory = "Active Directory"
    case okta = "Okta"
    case auth0 = "Auth0"
    case google = "Google Workspace"
    case microsoft = "Microsoft 365"
    case saml = "SAML 2.0"
    case oidc = "OpenID Connect"
}

struct SSOConfiguration: Codable {
    let provider: SSOProvider
    let entityId: String
    let ssoURL: String
    let certificateData: String
    let attributeMapping: [String: String]
    let isEnabled: Bool
}

enum MFAMethod: String, CaseIterable, Codable {
    case sms = "SMS"
    case email = "Email"
    case authenticatorApp = "Authenticator App"
    case hardwareToken = "Hardware Token"
    case biometric = "Biometric"
}

enum DataClassification: String, CaseIterable, Codable {
    case `public` = "Public"
    case `internal` = "Internal"
    case confidential = "Confidential"
    case restricted = "Restricted"
    case topSecret = "Top Secret"
}

struct EncryptedData: Codable {
    let encryptedData: Data
    let algorithm: EncryptionAlgorithm
    let keyId: UUID
    let classification: DataClassification
    let encryptedAt: Date

    init(encryptedData: Data, algorithm: EncryptionAlgorithm, keyId: UUID, classification: DataClassification) {
        self.encryptedData = encryptedData
        self.algorithm = algorithm
        self.keyId = keyId
        self.classification = classification
        self.encryptedAt = Date()
    }
}

enum EncryptionAlgorithm: String, CaseIterable, Codable {
    case aes256 = "AES-256"
    case rsa2048 = "RSA-2048"
    case rsa4096 = "RSA-4096"
    case ellipticCurve = "Elliptic Curve"
}

struct SecurityAuditReport: Identifiable, Codable {
    let id = UUID()
    let overallScore: Int // 0-100
    let vulnerabilities: [SecurityVulnerability]
    let recommendations: [String]
    let complianceStatus: ComplianceStatus
    let lastAuditDate: Date
    let nextAuditDate: Date

    init(overallScore: Int, vulnerabilities: [SecurityVulnerability], recommendations: [String], complianceStatus: ComplianceStatus, lastAuditDate: Date) {
        self.overallScore = overallScore
        self.vulnerabilities = vulnerabilities
        self.recommendations = recommendations
        self.complianceStatus = complianceStatus
        self.lastAuditDate = lastAuditDate
        self.nextAuditDate = Calendar.current.date(byAdding: .month, value: 3, to: lastAuditDate) ?? Date()
    }
}

struct SecurityVulnerability: Identifiable, Codable {
    let id = UUID()
    let severity: VulnerabilitySeverity
    let title: String
    let description: String
    let affectedSystems: [String]
    let remediation: String
    let discoveredAt: Date

    enum VulnerabilitySeverity: String, CaseIterable, Codable {
        case low = "Low"
        case medium = "Medium"
        case high = "High"
        case critical = "Critical"
    }
}

enum ComplianceStatus: String, CaseIterable, Codable {
    case compliant = "Compliant"
    case partiallyCompliant = "Partially Compliant"
    case nonCompliant = "Non-Compliant"
    case unknown = "Unknown"
}

enum ComplianceStandard: String, CaseIterable, Codable {
    case ferpa = "FERPA"
    case coppa = "COPPA"
    case gdpr = "GDPR"
    case hipaa = "HIPAA"
    case sox = "SOX"
    case iso27001 = "ISO 27001"
    case nist = "NIST"
}

struct ComplianceReport: Identifiable, Codable {
    let id = UUID()
    let standards: [ComplianceStandard]
    let complianceScore: Double // 0-1
    let violations: [ComplianceViolation]
    let recommendations: [String]
    let certifications: [ComplianceStandard]
    let generatedAt: Date
    let validUntil: Date

    init(standards: [ComplianceStandard], complianceScore: Double, violations: [ComplianceViolation], recommendations: [String], certifications: [ComplianceStandard], generatedAt: Date) {
        self.standards = standards
        self.complianceScore = complianceScore
        self.violations = violations
        self.recommendations = recommendations
        self.certifications = certifications
        self.generatedAt = generatedAt
        self.validUntil = Calendar.current.date(byAdding: .year, value: 1, to: generatedAt) ?? Date()
    }
}

struct ComplianceViolation: Identifiable, Codable {
    let id = UUID()
    let standard: ComplianceStandard
    let severity: ViolationSeverity
    let description: String
    let remediation: String
    let dueDate: Date

    enum ViolationSeverity: String, CaseIterable, Codable {
        case minor = "Minor"
        case major = "Major"
        case critical = "Critical"
    }
}

struct DataGovernancePolicy: Identifiable, Codable {
    let id = UUID()
    let name: String
    let description: String
    let dataTypes: [DataType]
    let retentionPeriod: Int // days
    let accessControls: [AccessControl]
    let isActive: Bool

    enum DataType: String, CaseIterable, Codable {
        case personalData = "Personal Data"
        case academicRecords = "Academic Records"
        case financialData = "Financial Data"
        case healthRecords = "Health Records"
        case communicationData = "Communication Data"
    }
}

struct AccessControl: Codable {
    let role: String
    let permissions: [Permission]
    let conditions: [AccessCondition]

    enum Permission: String, CaseIterable, Codable {
        case read = "Read"
        case write = "Write"
        case delete = "Delete"
        case export = "Export"
        case share = "Share"
    }
}

struct AccessCondition: Codable {
    let type: ConditionType
    let value: String

    enum ConditionType: String, CaseIterable, Codable {
        case timeRange = "Time Range"
        case ipAddress = "IP Address"
        case location = "Location"
        case deviceType = "Device Type"
        case mfaRequired = "MFA Required"
    }
}

struct AuditLoggingConfiguration: Codable {
    let enabledEvents: [AuditEvent]
    let retentionPeriod: Int // days
    let exportFormat: ExportFormat
    let realTimeAlerts: Bool

    enum AuditEvent: String, CaseIterable, Codable {
        case login = "Login"
        case logout = "Logout"
        case dataAccess = "Data Access"
        case dataModification = "Data Modification"
        case systemConfiguration = "System Configuration"
        case userManagement = "User Management"
        case reportGeneration = "Report Generation"
    }

    enum ExportFormat: String, CaseIterable, Codable {
        case json = "JSON"
        case csv = "CSV"
        case xml = "XML"
        case syslog = "Syslog"
    }
}

// MARK: - Performance Models

struct PerformanceMetrics: Codable {
    let responseTime: Int // milliseconds
    let throughput: Int // requests per second
    let errorRate: Double // 0-1
    let cpuUsage: Double // 0-1
    let memoryUsage: Double // 0-1
    let diskUsage: Double // 0-1
    let networkLatency: Int // milliseconds
    let generatedAt: Date

    init(responseTime: Int, throughput: Int, errorRate: Double, cpuUsage: Double, memoryUsage: Double, diskUsage: Double, networkLatency: Int) {
        self.responseTime = responseTime
        self.throughput = throughput
        self.errorRate = errorRate
        self.cpuUsage = cpuUsage
        self.memoryUsage = memoryUsage
        self.diskUsage = diskUsage
        self.networkLatency = networkLatency
        self.generatedAt = Date()
    }
}

struct AutoScalingConfiguration: Codable {
    let minInstances: Int
    let maxInstances: Int
    let targetCPUUtilization: Double // 0-1
    let targetMemoryUtilization: Double // 0-1
    let scaleUpCooldown: Int // seconds
    let scaleDownCooldown: Int // seconds
    let isEnabled: Bool
}

struct DatabaseOptimizationResult: Codable {
    let queriesOptimized: Int
    let performanceImprovement: Double // 0-1
    let indexesCreated: Int
    let storageReduced: Double // 0-1
    let recommendations: [String]
    let optimizedAt: Date

    init(queriesOptimized: Int, performanceImprovement: Double, indexesCreated: Int, storageReduced: Double, recommendations: [String]) {
        self.queriesOptimized = queriesOptimized
        self.performanceImprovement = performanceImprovement
        self.indexesCreated = indexesCreated
        self.storageReduced = storageReduced
        self.recommendations = recommendations
        self.optimizedAt = Date()
    }
}

enum CachingStrategy: String, CaseIterable, Codable {
    case redis = "Redis"
    case memcached = "Memcached"
    case inMemory = "In-Memory"
    case cdn = "CDN"
    case database = "Database"
    case hybrid = "Hybrid"
}

struct PerformanceReport: Identifiable, Codable {
    let id = UUID()
    let overallScore: Int // 0-100
    let bottlenecks: [String]
    let optimizations: [String]
    let trends: [PerformanceTrend]
    let generatedAt: Date

    init(overallScore: Int, bottlenecks: [String], optimizations: [String], trends: [PerformanceTrend]) {
        self.overallScore = overallScore
        self.bottlenecks = bottlenecks
        self.optimizations = optimizations
        self.trends = trends
        self.generatedAt = Date()
    }
}

struct PerformanceTrend: Codable {
    let metric: String
    let improvement: Double // 0-1
    let period: String

    init(metric: String, improvement: Double) {
        self.metric = metric
        self.improvement = improvement
        self.period = "Last 30 days"
    }
}

struct DataWarehouseConfiguration: Codable {
    let provider: DataWarehouseProvider
    let connectionString: String
    let syncFrequency: SyncFrequency
    let dataRetention: Int // days
    let compressionEnabled: Bool

    enum DataWarehouseProvider: String, CaseIterable, Codable {
        case snowflake = "Snowflake"
        case bigQuery = "Google BigQuery"
        case redshift = "Amazon Redshift"
        case synapse = "Azure Synapse"
        case databricks = "Databricks"
    }

    enum SyncFrequency: String, CaseIterable, Codable {
        case realTime = "Real-time"
        case hourly = "Hourly"
        case daily = "Daily"
        case weekly = "Weekly"
    }
}

// MARK: - Deployment Models

enum DeploymentStatus: String, CaseIterable, Codable {
    case active = "Active"
    case deploying = "Deploying"
    case maintenance = "Maintenance"
    case offline = "Offline"
    case error = "Error"
}

enum GlobalRegion: String, CaseIterable, Codable {
    case northAmerica = "North America"
    case southAmerica = "South America"
    case europe = "Europe"
    case asiaPacific = "Asia Pacific"
    case middleEast = "Middle East"
    case africa = "Africa"
    case oceania = "Oceania"
}

struct DeploymentConfiguration: Codable {
    let region: GlobalRegion
    let instanceType: InstanceType
    let autoScaling: Bool
    let loadBalancing: Bool
    let monitoring: Bool
    let backupEnabled: Bool
    let securityGroups: [String]

    enum InstanceType: String, CaseIterable, Codable {
        case small = "Small"
        case medium = "Medium"
        case large = "Large"
        case xlarge = "X-Large"
        case xxlarge = "XX-Large"
    }
}

struct CDNConfiguration: Codable {
    let provider: CDNProvider
    let cachePolicy: CachePolicy
    let compressionEnabled: Bool
    let sslEnabled: Bool
    let customDomains: [String]

    enum CDNProvider: String, CaseIterable, Codable {
        case cloudflare = "Cloudflare"
        case aws = "AWS CloudFront"
        case azure = "Azure CDN"
        case google = "Google Cloud CDN"
        case fastly = "Fastly"
    }

    enum CachePolicy: String, CaseIterable, Codable {
        case aggressive = "Aggressive"
        case moderate = "Moderate"
        case conservative = "Conservative"
        case custom = "Custom"
    }
}

enum LoadBalancingStrategy: String, CaseIterable, Codable {
    case roundRobin = "Round Robin"
    case leastConnections = "Least Connections"
    case weightedRoundRobin = "Weighted Round Robin"
    case ipHash = "IP Hash"
    case geolocation = "Geolocation"
}

struct GlobalHealthReport: Identifiable, Codable {
    let id = UUID()
    let regions: [RegionHealth]
    let overallHealth: HealthStatus
    let globalUptime: Double // Percentage
    let generatedAt: Date

    init(regions: [RegionHealth], overallHealth: HealthStatus, globalUptime: Double) {
        self.regions = regions
        self.overallHealth = overallHealth
        self.globalUptime = globalUptime
        self.generatedAt = Date()
    }
}

struct RegionHealth: Identifiable, Codable {
    let id = UUID()
    let region: GlobalRegion
    let status: HealthStatus
    let uptime: Double // Percentage
    let responseTime: Int // milliseconds
    let errorRate: Double // 0-1

    init(region: GlobalRegion, status: HealthStatus, uptime: Double) {
        self.region = region
        self.status = status
        self.uptime = uptime
        self.responseTime = Int.random(in: 50...200)
        self.errorRate = Double.random(in: 0...0.01)
    }
}

enum HealthStatus: String, CaseIterable, Codable {
    case healthy = "Healthy"
    case warning = "Warning"
    case critical = "Critical"
    case offline = "Offline"
}

// MARK: - Integration Models

struct EnterpriseIntegration: Identifiable, Codable {
    let id = UUID()
    let type: IntegrationType
    let name: String
    let isEnabled: Bool
    let configuration: [String: String]
    let lastSync: Date?
    let status: IntegrationStatus

    init(type: IntegrationType, name: String, isEnabled: Bool) {
        self.type = type
        self.name = name
        self.isEnabled = isEnabled
        self.configuration = [:]
        self.lastSync = isEnabled ? Date() : nil
        self.status = isEnabled ? .connected : .disconnected
    }
}

enum IntegrationType: String, CaseIterable, Codable {
    case sis = "Student Information System"
    case lms = "Learning Management System"
    case sso = "Single Sign-On"
    case analytics = "Analytics Platform"
    case communication = "Communication Platform"
    case assessment = "Assessment Platform"
    case gradebook = "Gradebook"
    case library = "Library System"
}

enum IntegrationStatus: String, CaseIterable, Codable {
    case connected = "Connected"
    case disconnected = "Disconnected"
    case syncing = "Syncing"
    case error = "Error"
    case pending = "Pending"
}

enum SISProvider: String, CaseIterable, Codable {
    case powerschool = "PowerSchool"
    case infinite = "Infinite Campus"
    case skyward = "Skyward"
    case clever = "Clever"
    case classlink = "ClassLink"
    case oneRoster = "OneRoster"
    case custom = "Custom"
}

struct SISConfiguration: Codable {
    let provider: SISProvider
    let apiEndpoint: String
    let apiKey: String
    let syncFrequency: SyncFrequency
    let dataMapping: [String: String]
    let isEnabled: Bool

    enum SyncFrequency: String, CaseIterable, Codable {
        case realTime = "Real-time"
        case hourly = "Hourly"
        case daily = "Daily"
        case weekly = "Weekly"
    }
}

enum LMSProvider: String, CaseIterable, Codable {
    case canvas = "Canvas"
    case blackboard = "Blackboard"
    case moodle = "Moodle"
    case schoology = "Schoology"
    case google = "Google Classroom"
    case microsoft = "Microsoft Teams"
    case custom = "Custom"
}

struct LMSConfiguration: Codable {
    let provider: LMSProvider
    let apiEndpoint: String
    let clientId: String
    let clientSecret: String
    let syncAssignments: Bool
    let syncGrades: Bool
    let syncRoster: Bool
}

struct APIGatewayConfiguration: Codable {
    let rateLimiting: RateLimitingConfig
    let authentication: AuthenticationConfig
    let logging: LoggingConfig
    let caching: CachingConfig
    let monitoring: MonitoringConfig
}

struct RateLimitingConfig: Codable {
    let requestsPerMinute: Int
    let requestsPerHour: Int
    let requestsPerDay: Int
    let burstLimit: Int
}

struct AuthenticationConfig: Codable {
    let requireApiKey: Bool
    let requireJWT: Bool
    let allowedOrigins: [String]
    let corsEnabled: Bool
}

struct LoggingConfig: Codable {
    let logRequests: Bool
    let logResponses: Bool
    let logErrors: Bool
    let retentionDays: Int
}

struct CachingConfig: Codable {
    let enabled: Bool
    let ttl: Int // seconds
    let maxSize: Int // MB
    let strategy: String
}

struct MonitoringConfig: Codable {
    let enabled: Bool
    let alertThresholds: AlertThresholds
    let dashboardEnabled: Bool
}

struct AlertThresholds: Codable {
    let errorRate: Double // 0-1
    let responseTime: Int // milliseconds
    let throughput: Int // requests/second
}

struct WebhookEndpoint: Identifiable, Codable {
    let id = UUID()
    let url: String
    let events: [WebhookEvent]
    let secret: String
    let isActive: Bool
    let retryPolicy: RetryPolicy

    enum WebhookEvent: String, CaseIterable, Codable {
        case userCreated = "user.created"
        case userUpdated = "user.updated"
        case userDeleted = "user.deleted"
        case assessmentCompleted = "assessment.completed"
        case gradeUpdated = "grade.updated"
        case reportGenerated = "report.generated"
        case systemAlert = "system.alert"
    }
}

struct RetryPolicy: Codable {
    let maxRetries: Int
    let backoffStrategy: BackoffStrategy
    let timeoutSeconds: Int

    enum BackoffStrategy: String, CaseIterable, Codable {
        case linear = "Linear"
        case exponential = "Exponential"
        case fixed = "Fixed"
    }
}

// MARK: - Business Intelligence Models

struct ExecutiveDashboard: Identifiable, Codable {
    let id = UUID()
    let totalRevenue: Double
    let totalStudents: Int
    let totalSchools: Int
    let customerSatisfaction: Double // 0-1
    let systemUptime: Double // Percentage
    let growthRate: Double // 0-1
    let churnRate: Double // 0-1
    let keyMetrics: [KeyMetric]
    let generatedAt: Date

    init(totalRevenue: Double, totalStudents: Int, totalSchools: Int, customerSatisfaction: Double, systemUptime: Double, growthRate: Double, churnRate: Double) {
        self.totalRevenue = totalRevenue
        self.totalStudents = totalStudents
        self.totalSchools = totalSchools
        self.customerSatisfaction = customerSatisfaction
        self.systemUptime = systemUptime
        self.growthRate = growthRate
        self.churnRate = churnRate
        self.keyMetrics = []
        self.generatedAt = Date()
    }
}

struct KeyMetric: Identifiable, Codable {
    let id = UUID()
    let name: String
    let value: Double
    let unit: String
    let trend: TrendDirection
    let change: Double

    enum TrendDirection: String, CaseIterable, Codable {
        case up = "Up"
        case down = "Down"
        case stable = "Stable"
    }
}

struct CustomReport: Identifiable {
    let id: UUID
    let name: String
    let type: ReportType
    let data: [String: String] // Changed from Any to String for Codable compliance
    let generatedAt: Date
    let generatedBy: UUID
    let format: ReportFormat

    init(id: UUID, name: String, type: ReportType, data: [String: String], generatedAt: Date) {
        self.id = id
        self.name = name
        self.type = type
        self.data = data
        self.generatedAt = generatedAt
        self.generatedBy = UUID() // Current user
        self.format = .pdf
    }

    enum ReportType: String, CaseIterable, Codable {
        case financial = "Financial"
        case academic = "Academic"
        case operational = "Operational"
        case compliance = "Compliance"
        case custom = "Custom"
    }

    enum ReportFormat: String, CaseIterable, Codable {
        case pdf = "PDF"
        case excel = "Excel"
        case csv = "CSV"
        case json = "JSON"
    }
}

// Manual Codable implementation for CustomReport
extension CustomReport: Codable {
    enum CodingKeys: String, CodingKey {
        case id, name, type, data, generatedAt, generatedBy, format
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.type = try container.decode(ReportType.self, forKey: .type)
        self.data = try container.decode([String: String].self, forKey: .data)
        self.generatedAt = try container.decode(Date.self, forKey: .generatedAt)
        self.generatedBy = try container.decode(UUID.self, forKey: .generatedBy)
        self.format = try container.decode(ReportFormat.self, forKey: .format)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(type, forKey: .type)
        try container.encode(data, forKey: .data)
        try container.encode(generatedAt, forKey: .generatedAt)
        try container.encode(generatedBy, forKey: .generatedBy)
        try container.encode(format, forKey: .format)
    }
}

struct ReportSpecification: Identifiable, Codable {
    let id = UUID()
    let name: String
    let type: CustomReport.ReportType
    let dataSource: String
    let filters: [ReportFilter]
    let groupBy: [String]
    let sortBy: [SortCriteria]
    let format: CustomReport.ReportFormat

    init(name: String, type: CustomReport.ReportType) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.dataSource = "default"
        self.filters = []
        self.groupBy = []
        self.sortBy = []
        self.format = .pdf
    }
}

struct SortCriteria: Codable {
    let field: String
    let direction: SortDirection

    enum SortDirection: String, CaseIterable, Codable {
        case ascending = "Ascending"
        case descending = "Descending"
    }
}
