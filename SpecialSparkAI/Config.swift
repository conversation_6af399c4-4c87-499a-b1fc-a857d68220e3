import Foundation

// MARK: - API Configuration
struct APIConfig {
    
    // MARK: - Gemini AI Configuration
    struct Gemini {
        // TODO: Add your Gemini API key here
        // Get your API key from: https://makersuite.google.com/app/apikey
        static let apiKey = "YOUR_GEMINI_API_KEY_HERE"
        static let baseURL = "https://generativelanguage.googleapis.com/v1beta"
        static let model = "gemini-1.5-flash-002"
    }
    
    // MARK: - Supabase Configuration  
    struct Supabase {
        // TODO: Add your Supabase configuration here
        // Get these from your Supabase project dashboard
        static let url = "YOUR_SUPABASE_URL_HERE"
        static let anonKey = "YOUR_SUPABASE_ANON_KEY_HERE"
        static let serviceRoleKey = "YOUR_SUPABASE_SERVICE_ROLE_KEY_HERE"
    }
    
    // MARK: - Development Configuration
    struct Development {
        static let useMockData = true // Set to false when using real APIs
        static let enableLogging = true
        static let debugMode = true
    }
    
    // MARK: - Validation
    static var isConfigured: Bool {
        return !Gemini.apiKey.contains("YOUR_") && 
               !Supabase.url.contains("YOUR_") &&
               !Supabase.anonKey.contains("YOUR_")
    }
    
    static func validateConfiguration() {
        if !isConfigured {
            print("⚠️ WARNING: API keys not configured!")
            print("📝 Please update Config.swift with your API keys:")
            print("   1. Gemini API Key: https://makersuite.google.com/app/apikey")
            print("   2. Supabase URL & Keys: https://supabase.com/dashboard")
        }
    }
}

// MARK: - Environment Helper
extension APIConfig {
    static func loadFromEnvironment() {
        // This allows loading from environment variables in production
        if let geminiKey = ProcessInfo.processInfo.environment["GEMINI_API_KEY"] {
            // In a real app, you'd use a more secure method to store this
            print("✅ Gemini API key loaded from environment")
        }
        
        if let supabaseURL = ProcessInfo.processInfo.environment["SUPABASE_URL"] {
            print("✅ Supabase URL loaded from environment")
        }
    }
}
