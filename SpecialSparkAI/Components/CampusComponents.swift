//
//  CampusComponents.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

// MARK: - Campus Area Card
struct CampusAreaCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let occupancy: Int
    let capacity: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                // Occupancy indicator
                HStack {
                    Spacer()
                    HStack(spacing: 5) {
                        Circle()
                            .fill(occupancyColor)
                            .frame(width: 8, height: 8)
                        Text("\(occupancy)/\(capacity)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Icon
                Image(systemName: icon)
                    .font(.largeTitle)
                    .foregroundColor(color)

                // Content
                VStack(spacing: 8) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                }

                // Occupancy bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 4)
                            .cornerRadius(2)

                        Rectangle()
                            .fill(color)
                            .frame(width: geometry.size.width * occupancyPercentage, height: 4)
                            .cornerRadius(2)
                    }
                }
                .frame(height: 4)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var occupancyPercentage: Double {
        guard capacity > 0 else { return 0 }
        return Double(occupancy) / Double(capacity)
    }

    private var occupancyColor: Color {
        let percentage = occupancyPercentage
        if percentage < 0.5 {
            return .green
        } else if percentage < 0.8 {
            return .yellow
        } else {
            return .red
        }
    }
}

// MARK: - Virtual Building Model
struct VirtualBuilding: Identifiable {
    let id = UUID()
    let name: String
    let description: String
    let icon: String
    let color: Color
    let floors: [BuildingFloor]
    let amenities: [String]
    let currentOccupancy: Int
    let maxCapacity: Int
}

struct BuildingFloor: Identifiable {
    let id = UUID()
    let number: Int
    let name: String
    let rooms: [BuildingRoom]
}

struct BuildingRoom: Identifiable {
    let id = UUID()
    let number: String
    let name: String
    let type: RoomType
    let capacity: Int
    let currentOccupancy: Int
    let amenities: [String]
}

// RoomType is defined in VirtualSchoolModels.swift to avoid conflicts

// MARK: - Building Detail View
struct BuildingDetailView: View {
    let building: VirtualBuilding
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Building header
                    buildingHeaderView

                    // Building stats
                    buildingStatsView

                    // Floors list
                    floorsListView

                    // Amenities
                    amenitiesView
                }
                .padding()
            }
            .navigationTitle(building.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var buildingHeaderView: some View {
        VStack(spacing: 15) {
            Image(systemName: building.icon)
                .font(.system(size: 60))
                .foregroundColor(building.color)

            Text(building.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }

    private var buildingStatsView: some View {
        HStack(spacing: 20) {
            StatView(title: "Floors", value: "\(building.floors.count)", icon: "building.2")
            StatView(title: "Occupancy", value: "\(building.currentOccupancy)/\(building.maxCapacity)", icon: "person.3")
            StatView(title: "Amenities", value: "\(building.amenities.count)", icon: "star")
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }

    private var floorsListView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Floors")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(building.floors.sorted(by: { $0.number < $1.number })) { floor in
                FloorRowView(floor: floor)
            }
        }
    }

    private var amenitiesView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Amenities")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 10) {
                ForEach(building.amenities, id: \.self) { amenity in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text(amenity)
                            .font(.subheadline)
                        Spacer()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(.ultraThinMaterial)
                    )
                }
            }
        }
    }
}

struct StatView: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct FloorRowView: View {
    let floor: BuildingFloor

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("Floor \(floor.number)")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(floor.rooms.count) rooms")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text(floor.name)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(.ultraThinMaterial)
        )
    }
}
