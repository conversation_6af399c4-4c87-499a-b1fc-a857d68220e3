//
//  UIComponents.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

// MARK: - Dashboard Components

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 10) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

struct ScheduleItemCard: View {
    let item: ScheduleItem

    var body: some View {
        HStack(spacing: 15) {
            // Time indicator
            VStack(spacing: 5) {
                Text(item.startTime.formatted(date: .omitted, time: .shortened))
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("\(item.duration)m")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(width: 50)

            // Subject indicator
            RoundedRectangle(cornerRadius: 8)
                .fill(subjectColor(for: item.subject))
                .frame(width: 4)

            // Content
            VStack(alignment: .leading, spacing: 5) {
                Text(item.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(item.subject)
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack {
                    Image(systemName: typeIcon(for: item.type))
                        .font(.caption2)
                    Text(typeText(for: item.type))
                        .font(.caption2)
                }
                .foregroundColor(.secondary)
            }

            Spacer()

            // Status indicator
            Circle()
                .fill(statusColor(for: item.startTime))
                .frame(width: 8, height: 8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 1)
        )
    }

    private func subjectColor(for subject: String) -> Color {
        switch subject.lowercased() {
        case "math", "mathematics":
            return .blue
        case "science":
            return .green
        case "english", "language arts":
            return .purple
        case "art":
            return .pink
        case "music":
            return .orange
        case "physical education", "pe":
            return .red
        default:
            return .gray
        }
    }

    private func typeIcon(for type: ScheduleItemType) -> String {
        switch type {
        case .lesson:
            return "book"
        case .activity:
            return "hands.sparkles"
        case .workshop:
            return "hammer"
        case .breakTime:
            return "cup.and.saucer"
        case .assembly:
            return "person.3"
        }
    }

    private func typeText(for type: ScheduleItemType) -> String {
        switch type {
        case .lesson:
            return "Lesson"
        case .activity:
            return "Activity"
        case .workshop:
            return "Workshop"
        case .breakTime:
            return "Break"
        case .assembly:
            return "Assembly"
        }
    }

    private func statusColor(for startTime: Date) -> Color {
        let now = Date()
        let timeDifference = startTime.timeIntervalSince(now)

        if timeDifference < 0 {
            return .gray // Past
        } else if timeDifference < 900 { // Within 15 minutes
            return .orange // Starting soon
        } else {
            return .green // Future
        }
    }
}

struct AchievementCard: View {
    let achievement: Achievement

    var body: some View {
        VStack(spacing: 10) {
            // Badge
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: categoryColors(for: achievement.category),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)

                Image(systemName: categoryIcon(for: achievement.category))
                    .font(.title2)
                    .foregroundColor(.white)
            }

            VStack(spacing: 5) {
                Text(achievement.title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)

                Text("\(achievement.points) pts")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(width: 100)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }

    private func categoryColors(for category: AchievementCategory) -> [Color] {
        switch category {
        case .academic:
            return [.blue, .cyan]
        case .social:
            return [.pink, .purple]
        case .creative:
            return [.orange, .yellow]
        case .leadership:
            return [.purple, .blue]
        case .perseverance:
            return [.green, .mint]
        case .kindness:
            return [.pink, .red]
        case .innovation:
            return [.indigo, .purple]
        case .collaboration:
            return [.teal, .blue]
        }
    }

    private func categoryIcon(for category: AchievementCategory) -> String {
        switch category {
        case .academic:
            return "graduationcap.fill"
        case .social:
            return "person.2.fill"
        case .creative:
            return "paintbrush.fill"
        case .leadership:
            return "crown.fill"
        case .perseverance:
            return "mountain.2.fill"
        case .kindness:
            return "heart.fill"
        case .innovation:
            return "lightbulb.fill"
        case .collaboration:
            return "hands.clap.fill"
        }
    }
}

struct AssignmentCard: View {
    let assignment: Assignment

    var body: some View {
        HStack(spacing: 15) {
            // Priority indicator
            RoundedRectangle(cornerRadius: 8)
                .fill(assignment.priority.color)
                .frame(width: 4)

            // Content
            VStack(alignment: .leading, spacing: 5) {
                Text(assignment.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(assignment.subject)
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack {
                    Image(systemName: "calendar")
                        .font(.caption2)
                    Text("Due \(assignment.dueDate.formatted(date: .abbreviated, time: .omitted))")
                        .font(.caption2)
                }
                .foregroundColor(.secondary)
            }

            Spacer()

            // Status
            if assignment.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title3)
            } else {
                Button(action: {
                    // Mark as completed
                }) {
                    Image(systemName: "circle")
                        .foregroundColor(.gray)
                        .font(.title3)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
                .shadow(radius: 1)
        )
    }
}

struct QuickActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                Image(systemName: icon)
                    .font(.largeTitle)
                    .foregroundColor(color)

                VStack(spacing: 5) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Virtual Campus Components
// CampusAreaCard is defined in CampusComponents.swift to avoid conflicts

// MARK: - Teacher Components

struct AITeacherCard: View {
    let teacher: AITeacher
    let isAvailable: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 15) {
                // Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: subjectGradient(for: teacher.subject),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Text(String(teacher.name.prefix(1)))
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    // Availability indicator
                    Circle()
                        .fill(isAvailable ? .green : .gray)
                        .frame(width: 20, height: 20)
                        .overlay(
                            Circle()
                                .stroke(.white, lineWidth: 2)
                        )
                        .offset(x: 25, y: 25)
                }

                // Info
                VStack(spacing: 8) {
                    Text(teacher.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)

                    Text(teacher.subject.rawValue)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    // Specializations
                    if !teacher.specializations.isEmpty {
                        Text(teacher.specializations.prefix(2).joined(separator: " • "))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }

                    // Status
                    HStack {
                        Circle()
                            .fill(isAvailable ? .green : .gray)
                            .frame(width: 6, height: 6)

                        Text(isAvailable ? "Available" : "In Class")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func subjectGradient(for subject: Subject) -> [Color] {
        switch subject {
        case .mathematics:
            return [.blue, .cyan]
        case .english:
            return [.purple, .pink]
        case .science:
            return [.green, .mint]
        case .socialStudies:
            return [.orange, .yellow]
        case .art:
            return [.pink, .red]
        case .music:
            return [.indigo, .purple]
        case .physicalEducation:
            return [.red, .orange]
        case .computerScience:
            return [.teal, .blue]
        case .foreignLanguage:
            return [.mint, .green]
        case .specialEducation:
            return [.purple, .blue]
        case .counseling:
            return [.pink, .purple]
        case .libraryScience:
            return [.brown, .orange]
        }
    }
}

// MARK: - Progress Components

struct ProgressRing: View {
    let progress: Double
    let color: Color
    let lineWidth: CGFloat

    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: lineWidth)

            Circle()
                .trim(from: 0, to: progress)
                .stroke(
                    color,
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1), value: progress)
        }
    }
}

struct SubjectProgressCard: View {
    let subject: String
    let progress: Double
    let level: String
    let color: Color

    var body: some View {
        VStack(spacing: 15) {
            HStack {
                Text(subject)
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text(level)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            ZStack {
                ProgressRing(progress: progress, color: color, lineWidth: 8)
                    .frame(width: 80, height: 80)

                VStack {
                    Text("\(Int(progress * 100))%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }
            }

            HStack {
                Text("Progress")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text("Keep going!")
                    .font(.caption)
                    .foregroundColor(color)
                    .fontWeight(.medium)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(radius: 3)
        )
    }
}
