//
//  AdaptiveLearningTests.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import XCTest
@testable import SpecialSparkAI

class AdaptiveLearningTests: XCTestCase {

    var adaptiveLearningService: AdaptiveLearningService!
    var mockStudentId: UUID!
    var mockSubjectId: UUID!
    var mockSkillId: UUID!

    override func setUp() {
        super.setUp()
        adaptiveLearningService = AdaptiveLearningService.shared
        mockStudentId = UUID()
        mockSubjectId = UUID()
        mockSkillId = UUID()
    }

    override func tearDown() {
        adaptiveLearningService = nil
        super.tearDown()
    }

    // MARK: - Difficulty Adaptation Tests

    func testDifficultyAdaptation_HighPerformance() async {
        // Given: High performance student
        let currentPerformance = 0.9
        let responseTime = 5.0 // Fast response
        let emotionalState = "confident"

        // When: Adapting difficulty
        let newDifficulty = await adaptiveLearningService.adaptDifficultyLevel(
            studentId: mockStudentId,
            subjectId: mockSubjectId,
            currentPerformance: currentPerformance,
            responseTime: responseTime,
            emotionalState: emotionalState
        )

        // Then: Should recommend harder difficulty
        XCTAssertEqual(newDifficulty, "harder", "High performing student should get harder content")
    }

    func testDifficultyAdaptation_LowPerformance() async {
        // Given: Low performance student
        let currentPerformance = 0.3
        let responseTime = 30.0 // Slow response
        let emotionalState = "frustrated"

        // When: Adapting difficulty
        let newDifficulty = await adaptiveLearningService.adaptDifficultyLevel(
            studentId: mockStudentId,
            subjectId: mockSubjectId,
            currentPerformance: currentPerformance,
            responseTime: responseTime,
            emotionalState: emotionalState
        )

        // Then: Should recommend easier difficulty
        XCTAssertEqual(newDifficulty, "easier", "Low performing student should get easier content")
    }

    // MARK: - Personalization Tests

    func testPersonalization_VisualLearner() async {
        // Given: Visual learner with ADHD
        let studentProfile = StudentProfile(
            id: mockStudentId,
            firstName: "Alex",
            lastName: "Smith",
            dateOfBirth: "2015-03-15",
            gradeLevel: "Grade 3",
            schoolLevel: "Elementary",
            profileImageURL: nil,
            parentEmail: "<EMAIL>",
            specialNeeds: ["ADHD", "Visual Processing"],
            learningStyle: "visual",
            preferredLanguage: "en",
            timezone: "UTC",
            isActive: true,
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z"
        )

        // When: Personalizing teaching approach
        let personalization = await adaptiveLearningService.personalizeTeachingApproach(
            for: studentProfile,
            subject: "Mathematics",
            currentTopic: "Addition"
        )

        // Then: Should include visual strategies
        XCTAssertTrue(personalization.visualAids.contains("Charts"), "Should include visual aids for visual learner")
        XCTAssertTrue(personalization.supportStrategies.contains("Frequent breaks"), "Should include ADHD support strategies")
    }

    // MARK: - Emotional State Tests

    func testEmotionalStateTracking_Frustration() async {
        // Given: Student showing frustration
        let emotionalIndicators = EmotionalIndicators(
            primaryState: "frustrated",
            confidence: 0.3,
            engagement: 0.2,
            frustration: 0.8,
            excitement: 0.1,
            context: "Math problem solving",
            triggers: ["Difficult problem", "Time pressure"]
        )

        // When: Tracking emotional state
        await adaptiveLearningService.trackEmotionalState(
            studentId: mockStudentId,
            sessionId: UUID(),
            indicators: emotionalIndicators
        )

        // Then: Should trigger interventions (verified through console logs)
        // Note: In a real test, we'd mock the database and verify the intervention was called
        XCTAssertTrue(true, "Emotional state tracking completed")
    }

    // MARK: - Progress Update Tests

    func testProgressUpdate_ImprovingStudent() async {
        // Given: Assessment result showing improvement
        let assessmentResult = AssessmentRecord(
            id: UUID(),
            studentId: mockStudentId,
            sessionId: UUID(),
            questionId: UUID(),
            questionText: "What is 5 + 3?",
            studentAnswer: "8",
            correctAnswer: "8",
            isCorrect: true,
            responseTime: 10.0,
            hintsUsed: 0,
            difficultyLevel: "medium",
            skillTested: "Addition",
            adaptiveAdjustment: nil,
            createdAt: "2024-01-01T00:00:00Z"
        )

        // When: Updating progress
        await adaptiveLearningService.updateLearningProgress(
            studentId: mockStudentId,
            subjectId: mockSubjectId,
            skillId: mockSkillId,
            assessmentResult: assessmentResult
        )

        // Then: Progress should be updated (verified through database in integration tests)
        XCTAssertTrue(true, "Progress update completed")
    }

    // MARK: - Recommendation Generation Tests

    func testRecommendationGeneration() async {
        // Given: Student needs adaptive recommendations
        await adaptiveLearningService.generateAdaptiveRecommendations(for: mockStudentId)

        // When: Recommendations are generated
        let recommendations = adaptiveLearningService.currentRecommendations

        // Then: Should have recommendations
        XCTAssertFalse(recommendations.isEmpty, "Should generate recommendations")

        // Verify recommendation structure
        if let firstRecommendation = recommendations.first {
            XCTAssertFalse(firstRecommendation.title.isEmpty, "Recommendation should have title")
            XCTAssertFalse(firstRecommendation.description.isEmpty, "Recommendation should have description")
            XCTAssertFalse(firstRecommendation.actionItems.isEmpty, "Recommendation should have action items")
        }
    }

    // MARK: - Integration Tests

    func testFullAdaptiveLearningCycle() async {
        // Given: A complete learning session scenario
        let studentProfile = StudentProfile(
            id: mockStudentId,
            firstName: "Emma",
            lastName: "Johnson",
            dateOfBirth: "2015-08-20",
            gradeLevel: "Grade 3",
            schoolLevel: "Elementary",
            profileImageURL: nil,
            parentEmail: "<EMAIL>",
            specialNeeds: ["Autism", "Sensory Processing"],
            learningStyle: "kinesthetic",
            preferredLanguage: "en",
            timezone: "UTC",
            isActive: true,
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z"
        )

        // When: Running full adaptive learning cycle

        // 1. Personalize teaching approach
        let personalization = await adaptiveLearningService.personalizeTeachingApproach(
            for: studentProfile,
            subject: "Science",
            currentTopic: "Plants"
        )

        // 2. Track emotional state
        let emotionalIndicators = EmotionalIndicators(
            primaryState: "engaged",
            confidence: 0.7,
            engagement: 0.8,
            frustration: 0.2,
            excitement: 0.7,
            context: "Science exploration",
            triggers: ["Hands-on activity"]
        )

        await adaptiveLearningService.trackEmotionalState(
            studentId: mockStudentId,
            sessionId: UUID(),
            indicators: emotionalIndicators
        )

        // 3. Adapt difficulty based on performance
        let newDifficulty = await adaptiveLearningService.adaptDifficultyLevel(
            studentId: mockStudentId,
            subjectId: mockSubjectId,
            currentPerformance: 0.75,
            responseTime: 15.0,
            emotionalState: "engaged"
        )

        // 4. Generate recommendations
        await adaptiveLearningService.generateAdaptiveRecommendations(for: mockStudentId)

        // Then: All components should work together
        XCTAssertEqual(personalization.teachingStyle, "visual-kinesthetic", "Should adapt to kinesthetic learner")
        XCTAssertEqual(newDifficulty, "maintain", "Should maintain difficulty for good performance")
        XCTAssertFalse(adaptiveLearningService.currentRecommendations.isEmpty, "Should generate recommendations")
    }

    // MARK: - Performance Tests

    func testAdaptiveLearningPerformance() {
        measure {
            // Test the performance of adaptive learning algorithms
            let expectation = XCTestExpectation(description: "Adaptive learning performance")

            Task {
                await adaptiveLearningService.generateAdaptiveRecommendations(for: mockStudentId)
                expectation.fulfill()
            }

            wait(for: [expectation], timeout: 5.0)
        }
    }
}

// MARK: - Mock Data Extensions

extension AdaptiveLearningTests {

    func createMockLearningSession() -> LearningSessionRecord {
        return LearningSessionRecord(
            id: UUID(),
            studentId: mockStudentId,
            subjectId: mockSubjectId,
            teacherAgentId: UUID(),
            sessionType: "practice",
            startTime: "2024-01-01T10:00:00Z",
            endTime: "2024-01-01T10:30:00Z",
            duration: 1800,
            topicsCovered: ["Addition", "Subtraction"],
            difficultyLevel: "medium",
            completionRate: 0.85,
            engagementScore: 0.75,
            emotionalState: "focused",
            adaptationsUsed: ["Visual aids", "Extra time"],
            createdAt: "2024-01-01T10:30:00Z"
        )
    }

    func createMockAssessmentResults() -> [AssessmentRecord] {
        return [
            AssessmentRecord(
                id: UUID(),
                studentId: mockStudentId,
                sessionId: UUID(),
                questionId: UUID(),
                questionText: "What is 7 + 5?",
                studentAnswer: "12",
                correctAnswer: "12",
                isCorrect: true,
                responseTime: 8.5,
                hintsUsed: 0,
                difficultyLevel: "medium",
                skillTested: "Addition",
                adaptiveAdjustment: nil,
                createdAt: "2024-01-01T10:15:00Z"
            ),
            AssessmentRecord(
                id: UUID(),
                studentId: mockStudentId,
                sessionId: UUID(),
                questionId: UUID(),
                questionText: "What is 15 - 8?",
                studentAnswer: "7",
                correctAnswer: "7",
                isCorrect: true,
                responseTime: 12.0,
                hintsUsed: 1,
                difficultyLevel: "medium",
                skillTested: "Subtraction",
                adaptiveAdjustment: "provide_hint",
                createdAt: "2024-01-01T10:20:00Z"
            )
        ]
    }
}
