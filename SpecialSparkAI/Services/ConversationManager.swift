//
//  ConversationManager.swift
//  SpecialSparkAI
//
//  Conversation Management and History System
//

import Foundation
import SwiftData

// MARK: - Conversation Models
@Model
class Conversation: Identifiable, Codable {
    @Attribute(.unique) var id: UUID
    var studentId: UUID
    var teacherId: UUID
    var subject: String
    var title: String
    var messages: [ConversationMessage]
    var startedAt: Date
    var lastMessageAt: Date
    var isActive: Bool
    var learningObjectives: [String]
    var assessmentNotes: String
    
    init(studentId: UUID, teacherId: UUID, subject: String, title: String = "") {
        self.id = UUID()
        self.studentId = studentId
        self.teacherId = teacherId
        self.subject = subject
        self.title = title.isEmpty ? "Conversation with \(subject) Teacher" : title
        self.messages = []
        self.startedAt = Date()
        self.lastMessageAt = Date()
        self.isActive = true
        self.learningObjectives = []
        self.assessmentNotes = ""
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, studentId, teacherId, subject, title, messages
        case startedAt, lastMessageAt, isActive, learningObjectives, assessmentNotes
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.studentId = try container.decode(UUID.self, forKey: .studentId)
        self.teacherId = try container.decode(UUID.self, forKey: .teacherId)
        self.subject = try container.decode(String.self, forKey: .subject)
        self.title = try container.decode(String.self, forKey: .title)
        self.messages = try container.decode([ConversationMessage].self, forKey: .messages)
        self.startedAt = try container.decode(Date.self, forKey: .startedAt)
        self.lastMessageAt = try container.decode(Date.self, forKey: .lastMessageAt)
        self.isActive = try container.decode(Bool.self, forKey: .isActive)
        self.learningObjectives = try container.decode([String].self, forKey: .learningObjectives)
        self.assessmentNotes = try container.decode(String.self, forKey: .assessmentNotes)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(studentId, forKey: .studentId)
        try container.encode(teacherId, forKey: .teacherId)
        try container.encode(subject, forKey: .subject)
        try container.encode(title, forKey: .title)
        try container.encode(messages, forKey: .messages)
        try container.encode(startedAt, forKey: .startedAt)
        try container.encode(lastMessageAt, forKey: .lastMessageAt)
        try container.encode(isActive, forKey: .isActive)
        try container.encode(learningObjectives, forKey: .learningObjectives)
        try container.encode(assessmentNotes, forKey: .assessmentNotes)
    }
}

struct ConversationMessage: Identifiable, Codable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let messageType: MessageType
    let metadata: MessageMetadata?
    
    init(content: String, isFromUser: Bool, messageType: MessageType = .text, metadata: MessageMetadata? = nil) {
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = Date()
        self.messageType = messageType
        self.metadata = metadata
    }
    
    enum MessageType: String, Codable, CaseIterable {
        case text = "text"
        case question = "question"
        case answer = "answer"
        case encouragement = "encouragement"
        case assessment = "assessment"
        case systemMessage = "system"
    }
    
    struct MessageMetadata: Codable {
        let confidence: Double?
        let learningObjective: String?
        let assessmentScore: Double?
        let emotionalTone: String?
        let difficulty: String?
    }
}

// MARK: - Conversation Manager
class ConversationManager: ObservableObject {
    static let shared = ConversationManager()
    
    @Published var activeConversations: [Conversation] = []
    @Published var currentConversation: Conversation?
    @Published var isLoading = false
    
    private let geminiService = GeminiService.shared
    private let supabaseService = SupabaseService.shared
    
    private init() {}
    
    // MARK: - Conversation Management
    func startConversation(
        student: Student,
        teacher: AITeacherPersonality,
        initialMessage: String? = nil
    ) async -> Conversation {
        
        let conversation = Conversation(
            studentId: student.id,
            teacherId: teacher.id,
            subject: teacher.subject,
            title: "Learning \(teacher.subject) with \(teacher.name)"
        )
        
        // Add initial teacher greeting
        let greeting = initialMessage ?? teacher.getRandomConversationStarter()
        let teacherMessage = ConversationMessage(
            content: greeting,
            isFromUser: false,
            messageType: .text
        )
        
        conversation.messages.append(teacherMessage)
        conversation.lastMessageAt = Date()
        
        await MainActor.run {
            self.activeConversations.append(conversation)
            self.currentConversation = conversation
        }
        
        // Save to Supabase if not using mock data
        if !APIConfig.Development.useMockData {
            await saveConversation(conversation)
        }
        
        return conversation
    }
    
    func sendMessage(
        _ content: String,
        to conversation: Conversation,
        student: Student,
        teacher: AITeacherPersonality
    ) async throws -> ConversationMessage {
        
        await MainActor.run {
            self.isLoading = true
        }
        
        defer {
            Task {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
        
        // Add user message
        let userMessage = ConversationMessage(
            content: content,
            isFromUser: true,
            messageType: .text
        )
        
        conversation.messages.append(userMessage)
        conversation.lastMessageAt = Date()
        
        // Generate AI response
        let systemPrompt = teacher.getPersonalizedSystemPrompt(for: student)
        let conversationHistory = Array(conversation.messages.suffix(APIConfig.Gemini.maxConversationHistory))
        
        let aiResponse = try await geminiService.generateResponse(
            prompt: content,
            context: systemPrompt,
            personality: teacher.name
        )
        
        // Create AI message
        let aiMessage = ConversationMessage(
            content: aiResponse,
            isFromUser: false,
            messageType: .answer,
            metadata: ConversationMessage.MessageMetadata(
                confidence: 0.9,
                learningObjective: nil,
                assessmentScore: nil,
                emotionalTone: "encouraging",
                difficulty: "appropriate"
            )
        )
        
        conversation.messages.append(aiMessage)
        conversation.lastMessageAt = Date()
        
        // Update UI
        await MainActor.run {
            if let index = self.activeConversations.firstIndex(where: { $0.id == conversation.id }) {
                self.activeConversations[index] = conversation
            }
            self.currentConversation = conversation
        }
        
        // Save to Supabase
        if !APIConfig.Development.useMockData {
            await saveConversation(conversation)
        }
        
        return aiMessage
    }
    
    func endConversation(_ conversation: Conversation) async {
        conversation.isActive = false
        conversation.lastMessageAt = Date()
        
        await MainActor.run {
            if let index = self.activeConversations.firstIndex(where: { $0.id == conversation.id }) {
                self.activeConversations[index] = conversation
            }
            
            if self.currentConversation?.id == conversation.id {
                self.currentConversation = nil
            }
        }
        
        // Save final state
        if !APIConfig.Development.useMockData {
            await saveConversation(conversation)
        }
    }
    
    // MARK: - Data Persistence
    private func saveConversation(_ conversation: Conversation) async {
        do {
            try await supabaseService.saveConversation(conversation)
        } catch {
            print("Failed to save conversation: \(error)")
        }
    }
    
    func loadConversations(for studentId: UUID) async {
        do {
            let conversations = try await supabaseService.getConversations(for: studentId)
            await MainActor.run {
                self.activeConversations = conversations
            }
        } catch {
            print("Failed to load conversations: \(error)")
        }
    }
    
    // MARK: - Conversation Analysis
    func analyzeConversation(_ conversation: Conversation) -> ConversationAnalysis {
        let totalMessages = conversation.messages.count
        let userMessages = conversation.messages.filter { $0.isFromUser }
        let aiMessages = conversation.messages.filter { !$0.isFromUser }
        
        let duration = conversation.lastMessageAt.timeIntervalSince(conversation.startedAt)
        let averageResponseTime = duration / Double(max(userMessages.count, 1))
        
        let engagementScore = calculateEngagementScore(conversation)
        let learningProgress = assessLearningProgress(conversation)
        
        return ConversationAnalysis(
            totalMessages: totalMessages,
            userMessages: userMessages.count,
            aiMessages: aiMessages.count,
            duration: duration,
            averageResponseTime: averageResponseTime,
            engagementScore: engagementScore,
            learningProgress: learningProgress,
            keyTopics: extractKeyTopics(conversation),
            recommendations: generateRecommendations(conversation)
        )
    }
    
    private func calculateEngagementScore(_ conversation: Conversation) -> Double {
        let messageCount = conversation.messages.filter { $0.isFromUser }.count
        let duration = conversation.lastMessageAt.timeIntervalSince(conversation.startedAt)
        
        // Simple engagement calculation based on message frequency
        let messagesPerMinute = Double(messageCount) / (duration / 60.0)
        return min(messagesPerMinute * 10, 100) // Scale to 0-100
    }
    
    private func assessLearningProgress(_ conversation: Conversation) -> Double {
        // Simple progress assessment based on conversation length and complexity
        let messageCount = conversation.messages.filter { $0.isFromUser }.count
        return min(Double(messageCount) * 5, 100) // Scale to 0-100
    }
    
    private func extractKeyTopics(_ conversation: Conversation) -> [String] {
        // Simple keyword extraction from user messages
        let userMessages = conversation.messages.filter { $0.isFromUser }
        let allText = userMessages.map { $0.content }.joined(separator: " ")
        
        // Basic topic extraction (in a real app, use NLP)
        let commonWords = ["math", "science", "reading", "problem", "question", "help", "learn"]
        return commonWords.filter { allText.lowercased().contains($0) }
    }
    
    private func generateRecommendations(_ conversation: Conversation) -> [String] {
        let analysis = analyzeConversation(conversation)
        var recommendations: [String] = []
        
        if analysis.engagementScore < 30 {
            recommendations.append("Try more interactive activities to increase engagement")
        }
        
        if analysis.userMessages < 5 {
            recommendations.append("Encourage more student participation")
        }
        
        if analysis.duration < 300 { // Less than 5 minutes
            recommendations.append("Consider longer learning sessions")
        }
        
        return recommendations
    }
}

// MARK: - Analysis Models
struct ConversationAnalysis {
    let totalMessages: Int
    let userMessages: Int
    let aiMessages: Int
    let duration: TimeInterval
    let averageResponseTime: TimeInterval
    let engagementScore: Double
    let learningProgress: Double
    let keyTopics: [String]
    let recommendations: [String]
}

// MARK: - Extensions
extension Conversation {
    var formattedDuration: String {
        let duration = lastMessageAt.timeIntervalSince(startedAt)
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    var messageCount: Int {
        return messages.count
    }
    
    var userMessageCount: Int {
        return messages.filter { $0.isFromUser }.count
    }
    
    var lastMessage: ConversationMessage? {
        return messages.last
    }
}
