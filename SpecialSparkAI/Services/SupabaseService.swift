//
//  SupabaseService.swift
//  SpecialSparkAI
//
//  Supabase Integration for Real-time Data Management
//

import Foundation

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()

    @Published var isConnected = false
    @Published var connectionError: String?

    private let supabaseURL = APIConfig.APIConfig.supabaseURL
    private let supabaseKey = APIConfig.APIConfig.supabaseAnonKey

    private var session: URLSession

    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        self.session = URLSession(configuration: config)
    }

    // MARK: - Initialization

    func initialize() async {
        do {
            // Test connection to Supabase
            let isReachable = await testConnection()

            await MainActor.run {
                self.isConnected = isReachable
                if !isReachable {
                    self.connectionError = "Unable to connect to Supabase"
                }
            }
        }
    }

    private func testConnection() async -> Bool {
        guard let url = URL(string: "\(supabaseURL)/rest/v1/") else { return false }

        var request = URLRequest(url: url)
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (_, response) = try await session.data(for: request)
            return (response as? HTTPURLResponse)?.statusCode == 200
        } catch {
            return false
        }
    }

    // MARK: - Agent Management

    func saveAgent(_ agent: AIAgent) async {
        let agentData = AgentData(from: agent)
        await saveToTable("ai_agents", data: agentData)
    }

    func updateAgent(_ agent: AIAgent) async {
        let agentData = AgentData(from: agent)
        await updateInTable("ai_agents", id: agent.id.uuidString, data: agentData)
    }

    func loadActiveAgents() async -> [AIAgent] {
        let agentsData: [AgentData] = await loadFromTable("ai_agents", filter: "is_active.eq.true")
        return agentsData.map { $0.toAIAgent() }
    }

    func deleteAgent(_ agentId: UUID) async {
        await deleteFromTable("ai_agents", id: agentId.uuidString)
    }

    // MARK: - Interaction Management

    func saveInteraction(_ interaction: LearningInteraction) async {
        let interactionData = InteractionData(from: interaction)
        await saveToTable("learning_interactions", data: interactionData)
    }

    func updateInteraction(_ interaction: LearningInteraction) async {
        let interactionData = InteractionData(from: interaction)
        await updateInTable("learning_interactions", id: interaction.id.uuidString, data: interactionData)
    }

    func loadInteractions(for agentId: UUID) async -> [LearningInteraction] {
        let interactionsData: [InteractionData] = await loadFromTable(
            "learning_interactions",
            filter: "agent_id.eq.\(agentId.uuidString)"
        )
        return interactionsData.map { $0.toLearningInteraction() }
    }

    func loadInteractionsForStudent(_ studentId: UUID) async -> [LearningInteraction] {
        let interactionsData: [InteractionData] = await loadFromTable(
            "learning_interactions",
            filter: "student_id.eq.\(studentId.uuidString)"
        )
        return interactionsData.map { $0.toLearningInteraction() }
    }

    // MARK: - Crew Management

    func saveCrew(_ crew: AgentCrew) async {
        let crewData = CrewData(from: crew)
        await saveToTable("agent_crews", data: crewData)
    }

    func updateCrew(_ crew: AgentCrew) async {
        let crewData = CrewData(from: crew)
        await updateInTable("agent_crews", id: crew.id.uuidString, data: crewData)
    }

    func loadCrews() async -> [AgentCrew] {
        let crewsData: [CrewData] = await loadFromTable("agent_crews")
        return crewsData.map { $0.toAgentCrew() }
    }

    // MARK: - Workflow Management

    func saveWorkflow(_ workflow: LearningWorkflow) async {
        let workflowData = WorkflowData(from: workflow)
        await saveToTable("learning_workflows", data: workflowData)
    }

    func updateWorkflow(_ workflow: LearningWorkflow) async {
        let workflowData = WorkflowData(from: workflow)
        await updateInTable("learning_workflows", id: workflow.id.uuidString, data: workflowData)
    }

    func loadActiveWorkflows() async -> [LearningWorkflow] {
        let workflowsData: [WorkflowData] = await loadFromTable(
            "learning_workflows",
            filter: "is_complete.eq.false"
        )
        return workflowsData.map { $0.toLearningWorkflow() }
    }

    // MARK: - Real-time Subscriptions

    func subscribeToAgentUpdates(completion: @escaping ([AIAgent]) -> Void) {
        // Implement real-time subscription to agent updates
        // This would use Supabase real-time features
    }

    func subscribeToInteractionUpdates(completion: @escaping ([LearningInteraction]) -> Void) {
        // Implement real-time subscription to interaction updates
    }

    // MARK: - Generic Database Operations

    private func saveToTable<T: Codable>(_ table: String, data: T) async {
        guard let url = URL(string: "\(supabaseURL)/rest/v1/\(table)") else { return }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("return=minimal", forHTTPHeaderField: "Prefer")

        do {
            let jsonData = try JSONEncoder().encode(data)
            request.httpBody = jsonData

            let (_, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode >= 400 {
                print("Error saving to \(table): \(httpResponse.statusCode)")
            }
        } catch {
            print("Error encoding/sending data to \(table): \(error)")
        }
    }

    private func updateInTable<T: Codable>(_ table: String, id: String, data: T) async {
        guard let url = URL(string: "\(supabaseURL)/rest/v1/\(table)?id=eq.\(id)") else { return }

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("return=minimal", forHTTPHeaderField: "Prefer")

        do {
            let jsonData = try JSONEncoder().encode(data)
            request.httpBody = jsonData

            let (_, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode >= 400 {
                print("Error updating \(table): \(httpResponse.statusCode)")
            }
        } catch {
            print("Error encoding/sending update to \(table): \(error)")
        }
    }

    private func loadFromTable<T: Codable>(_ table: String, filter: String = "") async -> [T] {
        var urlString = "\(supabaseURL)/rest/v1/\(table)"
        if !filter.isEmpty {
            urlString += "?\(filter)"
        }

        guard let url = URL(string: urlString) else { return [] }

        var request = URLRequest(url: url)
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode >= 400 {
                print("Error loading from \(table): \(httpResponse.statusCode)")
                return []
            }

            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode([T].self, from: data)
        } catch {
            print("Error loading/decoding from \(table): \(error)")
            return []
        }
    }

    private func deleteFromTable(_ table: String, id: String) async {
        guard let url = URL(string: "\(supabaseURL)/rest/v1/\(table)?id=eq.\(id)") else { return }

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")

        do {
            let (_, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode >= 400 {
                print("Error deleting from \(table): \(httpResponse.statusCode)")
            }
        } catch {
            print("Error deleting from \(table): \(error)")
        }
    }
}

// MARK: - Data Transfer Objects

struct AgentData: Codable {
    let id: String
    let name: String
    let agent_type: String
    let specialization: String
    let personality: String // JSON
    let capabilities: String // JSON
    let current_state: String
    let memory_context: String
    let is_active: Bool
    let created_date: Date
    let last_interaction: Date
    let workflow_state: String
    let graph_configuration: String
    let role: String
    let goal: String
    let backstory: String
    let tools: String // JSON array

    init(from agent: AIAgent) {
        self.id = agent.id.uuidString
        self.name = agent.name
        self.agent_type = agent.agentType.rawValue
        self.specialization = agent.specialization
        self.personality = "" // Convert personality to JSON
        self.capabilities = "" // Convert capabilities to JSON
        self.current_state = agent.currentState.rawValue
        self.memory_context = agent.memoryContext
        self.is_active = agent.isActive
        self.created_date = agent.createdDate
        self.last_interaction = agent.lastInteraction
        self.workflow_state = agent.workflowState
        self.graph_configuration = agent.graphConfiguration
        self.role = agent.role
        self.goal = agent.goal
        self.backstory = agent.backstory
        self.tools = "" // Convert tools to JSON
    }

    func toAIAgent() -> AIAgent {
        let agent = AIAgent(
            name: name,
            agentType: AgentType(rawValue: agent_type) ?? .subjectSpecialist,
            specialization: specialization
        )
        agent.id = UUID(uuidString: id) ?? UUID()
        agent.currentState = AgentState(rawValue: current_state) ?? .idle
        agent.memoryContext = memory_context
        agent.isActive = is_active
        agent.createdDate = created_date
        agent.lastInteraction = last_interaction
        agent.workflowState = workflow_state
        agent.graphConfiguration = graph_configuration
        agent.role = role
        agent.goal = goal
        agent.backstory = backstory
        return agent
    }
}

struct InteractionData: Codable {
    let id: String
    let agent_id: String
    let student_id: String
    let interaction_type: String
    let content: String
    let context: String
    let outcome: String
    let duration: Int
    let timestamp: Date
    let emotional_state: String
    let learning_objectives: String // JSON array
    let adaptations_used: String // JSON array

    init(from interaction: LearningInteraction) {
        self.id = interaction.id.uuidString
        self.agent_id = interaction.agentId.uuidString
        self.student_id = interaction.studentId.uuidString
        self.interaction_type = interaction.interactionType.rawValue
        self.content = interaction.content
        self.context = interaction.context
        self.outcome = interaction.outcome.rawValue
        self.duration = interaction.duration
        self.timestamp = interaction.timestamp
        self.emotional_state = interaction.emotionalState.rawValue
        self.learning_objectives = "" // Convert to JSON
        self.adaptations_used = "" // Convert to JSON
    }

    func toLearningInteraction() -> LearningInteraction {
        let interaction = LearningInteraction(
            agentId: UUID(uuidString: agent_id) ?? UUID(),
            studentId: UUID(uuidString: student_id) ?? UUID(),
            interactionType: AgentInteractionType(rawValue: interaction_type) ?? .lesson,
            content: content
        )
        interaction.id = UUID(uuidString: id) ?? UUID()
        interaction.context = context
        interaction.outcome = AgentInteractionOutcome(rawValue: outcome) ?? .pending
        interaction.duration = duration
        interaction.timestamp = timestamp
        interaction.emotionalState = EmotionalState(rawValue: emotional_state) ?? .neutral
        return interaction
    }
}

struct CrewData: Codable {
    let id: String
    let name: String
    let purpose: String
    let agents: String // JSON array of UUIDs
    let current_task: String?
    let is_active: Bool
    let created_date: Date

    init(from crew: AgentCrew) {
        self.id = crew.id.uuidString
        self.name = crew.name
        self.purpose = crew.purpose
        self.agents = "" // Convert agents array to JSON
        self.current_task = crew.currentTask
        self.is_active = crew.isActive
        self.created_date = crew.createdDate
    }

    func toAgentCrew() -> AgentCrew {
        let crew = AgentCrew(name: name, purpose: purpose)
        crew.id = UUID(uuidString: id) ?? UUID()
        crew.currentTask = current_task
        crew.isActive = is_active
        crew.createdDate = created_date
        return crew
    }
}

struct WorkflowData: Codable {
    let id: String
    let agent_id: String
    let student_id: String
    let workflow_type: String
    let current_node: String
    let node_history: String // JSON array
    let state: String
    let is_complete: Bool
    let start_time: Date
    let end_time: Date?
    let objectives: String // JSON array
    let adaptations: String // JSON array

    init(from workflow: LearningWorkflow) {
        self.id = workflow.id.uuidString
        self.agent_id = workflow.agentId.uuidString
        self.student_id = workflow.studentId.uuidString
        self.workflow_type = workflow.workflowType.rawValue
        self.current_node = workflow.currentNode
        self.node_history = "" // Convert to JSON
        self.state = workflow.state
        self.is_complete = workflow.isComplete
        self.start_time = workflow.startTime
        self.end_time = workflow.endTime
        self.objectives = "" // Convert to JSON
        self.adaptations = "" // Convert to JSON
    }

    func toLearningWorkflow() -> LearningWorkflow {
        let workflow = LearningWorkflow(
            agentId: UUID(uuidString: agent_id) ?? UUID(),
            studentId: UUID(uuidString: student_id) ?? UUID(),
            workflowType: WorkflowType(rawValue: workflow_type) ?? .adaptiveLearning
        )
        workflow.id = UUID(uuidString: id) ?? UUID()
        workflow.currentNode = current_node
        workflow.state = state
        workflow.isComplete = is_complete
        workflow.startTime = start_time
        workflow.endTime = end_time
        return workflow
    }
}
