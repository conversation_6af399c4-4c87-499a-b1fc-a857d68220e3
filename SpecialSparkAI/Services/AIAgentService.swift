//
//  AIAgentService.swift
//  SpecialSparkAI
//
//  AI Agent Service for LangGraph and CrewAI Integration
//

import Foundation
import SwiftData

@MainActor
class AIAgentService: ObservableObject {
    @Published var activeAgents: [AIAgent] = []
    @Published var currentInteractions: [LearningInteraction] = []
    @Published var isProcessing = false
    @Published var connectionStatus: ConnectionStatus = .disconnected

    private let supabaseService = SupabaseService.shared
    private let geminiService = GeminiService.shared
    private let langGraphService = LangGraphService.shared
    private let crewAIService = CrewAIService.shared

    enum ConnectionStatus {
        case connected
        case disconnected
        case connecting
        case error(String)
    }

    // MARK: - Initialization

    init() {
        Task {
            await initializeServices()
            await loadActiveAgents()
        }
    }

    private func initializeServices() async {
        connectionStatus = .connecting

        do {
            // Initialize Supabase connection
            await supabaseService.initialize()

            // Initialize Gemini Flash 2.0
            await geminiService.initialize()

            // Initialize LangGraph workflows
            await langGraphService.initialize()

            // Initialize CrewAI framework
            await crewAIService.initialize()

            connectionStatus = .connected
        } catch {
            connectionStatus = .error(error.localizedDescription)
        }
    }

    // MARK: - Agent Management

    func createAgent(
        name: String,
        type: AgentType,
        specialization: String,
        personality: AgentPersonality? = nil
    ) async -> AIAgent {
        let agent = AIAgent(name: name, agentType: type, specialization: specialization)

        if let personality = personality {
            agent.personality = personality
        }

        // Configure agent capabilities based on type
        agent.capabilities = await generateCapabilities(for: agent)

        // Register agent with LangGraph
        await langGraphService.registerAgent(agent)

        // Register agent with CrewAI
        await crewAIService.registerAgent(agent)

        // Save to Supabase
        await supabaseService.saveAgent(agent)

        activeAgents.append(agent)
        return agent
    }

    func activateAgent(_ agent: AIAgent) async {
        agent.isActive = true
        agent.currentState = .idle

        await supabaseService.updateAgent(agent)
        await langGraphService.activateAgent(agent)
        await crewAIService.activateAgent(agent)
    }

    func deactivateAgent(_ agent: AIAgent) async {
        agent.isActive = false
        agent.currentState = .idle

        await supabaseService.updateAgent(agent)
        await langGraphService.deactivateAgent(agent)
        await crewAIService.deactivateAgent(agent)
    }

    // MARK: - Learning Interactions

    func startLearningSession(
        with agent: AIAgent,
        for student: Student,
        objective: String
    ) async -> LearningInteraction {
        let interaction = LearningInteraction(
            agentId: agent.id,
            studentId: student.id,
            interactionType: .lesson,
            content: objective
        )

        // Update agent state
        agent.currentState = .teaching
        agent.lastInteraction = Date()

        // Start LangGraph workflow
        _ = await langGraphService.startLearningWorkflow(
            agent: agent,
            student: student,
            objective: objective
        )

        // Log interaction
        currentInteractions.append(interaction)
        await supabaseService.saveInteraction(interaction)

        return interaction
    }

    func processStudentInput(
        _ input: String,
        interaction: LearningInteraction,
        emotionalState: EmotionalState = .neutral
    ) async -> String {
        isProcessing = true

        do {
            // Get the agent
            guard let agent = activeAgents.first(where: { $0.id == interaction.agentId }) else {
                throw AIAgentError.agentNotFound
            }

            // Update emotional state
            interaction.emotionalState = emotionalState

            // Process through LangGraph workflow
            let response = await langGraphService.processInput(
                input: input,
                agent: agent,
                interaction: interaction
            )

            // Generate response using Gemini Flash 2.0
            let aiResponse = await geminiService.generateResponse(
                prompt: response.prompt,
                context: response.context,
                personality: "\(agent.personality.warmth), \(agent.personality.patience)"
            )

            // Update interaction
            interaction.content += "\nStudent: \(input)\nAgent: \(aiResponse)"
            interaction.duration += Int(Date().timeIntervalSince(interaction.timestamp))

            // Save to Supabase
            await supabaseService.updateInteraction(interaction)

            isProcessing = false
            return aiResponse

        } catch {
            isProcessing = false
            return "I'm sorry, I'm having trouble processing that right now. Could you try again?"
        }
    }

    // MARK: - Agent Collaboration (CrewAI)

    func createAgentCrew(
        name: String,
        purpose: String,
        agents: [AIAgent]
    ) async -> AgentCrew {
        let crew = AgentCrew(name: name, purpose: purpose)
        crew.agents = agents.map { $0.id }

        // Register crew with CrewAI
        await crewAIService.createCrew(crew, with: agents)

        // Save to Supabase
        await supabaseService.saveCrew(crew)

        return crew
    }

    func executeCrewTask(
        crew: AgentCrew,
        task: String,
        for student: Student
    ) async -> String {
        // Get crew agents
        let crewAgents = activeAgents.filter { crew.agents.contains($0.id) }

        // Execute collaborative task
        let result = await crewAIService.executeTask(
            task: task,
            agents: crewAgents,
            student: student
        )

        // Log crew interaction
        let interaction = CrewInteraction(
            crewId: crew.id,
            initiatingAgentId: crewAgents.first?.id ?? UUID(),
            task: task
        )
        interaction.outcome = result
        interaction.participatingAgents = crewAgents.map { $0.id }

        crew.collaborationHistory.append(interaction)
        await supabaseService.updateCrew(crew)

        return result
    }

    // MARK: - Adaptive Learning

    func adaptAgentBehavior(
        _ agent: AIAgent,
        based on: [LearningInteraction]
    ) async {
        // Analyze interaction patterns
        let interactions = on // Use the provided interactions parameter
        let analysisText = await geminiService.analyzeInteractionPatterns(on: interactions)

        // Create InteractionAnalysis from the text response
        let analysis = InteractionAnalysis(
            needsMorePatience: analysisText.contains("patience"),
            needsMoreEnthusiasm: analysisText.contains("enthusiasm"),
            needsDifferentCommunicationStyle: analysisText.contains("communication"),
            recommendedStyle: .adaptive,
            recommendedAdaptations: ["personalized_pacing", "visual_aids"],
            overallEffectiveness: 0.8
        )

        // Update agent personality and capabilities
        await updateAgentPersonality(agent, with: analysis)

        // Update LangGraph workflows
        let learningAnalysis = LearningAnalysis(
            studentId: "",
            performanceLevel: analysis.overallEffectiveness > 0.7 ? "High" : "Medium",
            learningStyle: analysis.recommendedStyle.rawValue,
            strengths: [],
            challenges: [],
            recommendations: analysis.recommendedAdaptations,
            adaptations: analysis.recommendedAdaptations
        )
        await langGraphService.updateWorkflows(for: agent, with: learningAnalysis)

        // Save changes
        await supabaseService.updateAgent(agent)
    }

    // MARK: - Private Helper Methods

    private func loadActiveAgents() async {
        do {
            activeAgents = await supabaseService.loadActiveAgents()
        } catch {
            print("Error loading agents: \(error)")
        }
    }

    private func generateCapabilities(for agent: AIAgent) async -> [AgentCapability] {
        let baseCapabilities = agent.agentType.defaultTools.map { tool in
            AgentCapability(
                agentId: agent.id,
                name: tool,
                details: "AI-powered \(tool) capability"
            )
        }

        // Use Gemini to generate additional specialized capabilities
        let additionalCapabilityNames = await geminiService.generateCapabilities(
            for: "Grade 3",
            subject: agent.specialization,
            specialNeeds: []
        )

        let additionalCapabilities = additionalCapabilityNames.map { capabilityName in
            AgentCapability(
                agentId: agent.id,
                name: capabilityName,
                details: "AI-generated \(capabilityName) capability"
            )
        }

        return baseCapabilities + additionalCapabilities
    }

    private func updateAgentPersonality(
        _ agent: AIAgent,
        with analysis: InteractionAnalysis
    ) async {
        // Adjust personality traits based on student interactions
        if analysis.needsMorePatience {
            agent.personality.patience = min(10, agent.personality.patience + 1)
        }

        if analysis.needsMoreEnthusiasm {
            agent.personality.enthusiasm = min(10, agent.personality.enthusiasm + 1)
        }

        if analysis.needsDifferentCommunicationStyle {
            agent.personality.communicationStyle = analysis.recommendedStyle
        }

        // Update special needs adaptations
        agent.personality.specialNeedsAdaptations.append(contentsOf: analysis.recommendedAdaptations)
    }
}

// MARK: - Supporting Types

enum AIAgentError: Error {
    case agentNotFound
    case serviceNotInitialized
    case invalidConfiguration
    case networkError
    case processingError
}


