//
//  WorkingGeminiAgent.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI

// MARK: - Working Gemini Agent for Testing
class WorkingGeminiAgent: ObservableObject {
    @Published var isLoading = false
    @Published var lastResponse = ""
    @Published var error: String?
    
    private let apiKey = APIConfig.geminiAPIKey
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"
    
    func askQuestion(_ question: String, studentName: String = "Student", gradeLevel: String = "3rd Grade") async {
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let response = await callGeminiAPI(question: question, studentName: studentName, gradeLevel: gradeLevel)
            await MainActor.run {
                self.lastResponse = response
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    private func callGeminiAPI(question: String, studentName: String, gradeLevel: String) async -> String {
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            return "Error: Invalid URL"
        }
        
        let prompt = buildEducationalPrompt(question: question, studentName: studentName, gradeLevel: gradeLevel)
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 1024
            ],
            "safetySettings": [
                [
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                ],
                [
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                ],
                [
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                ],
                [
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                ]
            ]
        ]
        
        do {
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("📡 Gemini API Response Status: \(httpResponse.statusCode)")
            }
            
            if let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                return parseGeminiResponse(jsonResponse)
            } else {
                return "Error: Could not parse response"
            }
        } catch {
            print("❌ Gemini API Error: \(error)")
            return "Error: \(error.localizedDescription)"
        }
    }
    
    private func buildEducationalPrompt(question: String, studentName: String, gradeLevel: String) -> String {
        return """
        You are an AI teacher assistant for SpecialSpark AI, a virtual school designed to help special needs children learn effectively.
        
        Student Information:
        - Name: \(studentName)
        - Grade Level: \(gradeLevel)
        
        Instructions:
        - Be encouraging, patient, and supportive
        - Use age-appropriate language for \(gradeLevel)
        - Break down complex concepts into simple steps
        - Use examples and analogies that children can relate to
        - Be positive and celebrate learning efforts
        - If the student seems frustrated, offer encouragement
        - Keep responses concise but helpful
        
        Student Question: \(question)
        
        Please provide a helpful, educational response that's perfect for this student's grade level and learning needs.
        """
    }
    
    private func parseGeminiResponse(_ json: [String: Any]) -> String {
        if let candidates = json["candidates"] as? [[String: Any]],
           let firstCandidate = candidates.first,
           let content = firstCandidate["content"] as? [String: Any],
           let parts = content["parts"] as? [[String: Any]],
           let firstPart = parts.first,
           let text = firstPart["text"] as? String {
            return text.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // Check for errors
        if let error = json["error"] as? [String: Any],
           let message = error["message"] as? String {
            return "API Error: \(message)"
        }
        
        return "Error: Could not parse response from Gemini API"
    }
}

// MARK: - Test View for Gemini Agent
struct GeminiTestView: View {
    @StateObject private var agent = WorkingGeminiAgent()
    @State private var question = ""
    @State private var studentName = "Alex"
    @State private var gradeLevel = "3rd Grade"
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Student Info
                VStack(alignment: .leading, spacing: 10) {
                    Text("Student Information")
                        .font(.headline)
                    
                    HStack {
                        Text("Name:")
                        TextField("Student Name", text: $studentName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    
                    HStack {
                        Text("Grade:")
                        TextField("Grade Level", text: $gradeLevel)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)
                
                // Question Input
                VStack(alignment: .leading, spacing: 10) {
                    Text("Ask Your AI Teacher")
                        .font(.headline)
                    
                    TextField("What would you like to learn about?", text: $question, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                }
                
                // Ask Button
                Button(action: {
                    Task {
                        await agent.askQuestion(question, studentName: studentName, gradeLevel: gradeLevel)
                    }
                }) {
                    HStack {
                        if agent.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "brain.head.profile")
                        }
                        Text(agent.isLoading ? "Thinking..." : "Ask AI Teacher")
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(question.isEmpty ? Color.gray : Color.blue)
                    .cornerRadius(10)
                }
                .disabled(question.isEmpty || agent.isLoading)
                
                // Response Area
                ScrollView {
                    VStack(alignment: .leading, spacing: 10) {
                        if let error = agent.error {
                            Text("Error: \(error)")
                                .foregroundColor(.red)
                                .padding()
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(8)
                        }
                        
                        if !agent.lastResponse.isEmpty {
                            Text("AI Teacher Response:")
                                .font(.headline)
                            
                            Text(agent.lastResponse)
                                .padding()
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("AI Teacher Test")
        }
    }
}

// MARK: - Preview
struct GeminiTestView_Previews: PreviewProvider {
    static var previews: some View {
        GeminiTestView()
    }
}
