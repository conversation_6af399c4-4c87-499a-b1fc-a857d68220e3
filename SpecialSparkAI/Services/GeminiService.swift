//
//  GeminiService.swift
//  SpecialSparkAI
//
//  Gemini Flash 2.0 Integration for AI Agent Intelligence
//

import Foundation
import SwiftData

class GeminiService: ObservableObject {
    static let shared = GeminiService()

    @Published var isInitialized = false
    @Published var isProcessing = false
    @Published var lastError: String?

    private let apiKey = "YOUR_GEMINI_API_KEY" // Replace with your Gemini API key
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta"
    private var session: URLSession

    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60
        config.timeoutIntervalForResource = 120
        self.session = URLSession(configuration: config)
    }

    // MARK: - Initialization

    func initialize() async {
        do {
            let isValid = await validateAPIKey()
            await MainActor.run {
                self.isInitialized = isValid
                if !isValid {
                    self.lastError = "Invalid Gemini API key"
                }
            }
        }
    }

    private func validateAPIKey() async -> Bool {
        let testPrompt = "Hello, this is a test."
        let response = await generateResponse(prompt: testPrompt, context: "", personality: nil)
        return !response.isEmpty && response != "Error generating response"
    }

    // MARK: - Core AI Generation

    func generateResponse(
        prompt: String,
        context: String,
        personality: String? = nil
    ) async -> String {
        await MainActor.run { self.isProcessing = true }
        defer { Task { await MainActor.run { self.isProcessing = false } } }

        let systemPrompt = buildSystemPrompt(personality: personality)
        let fullPrompt = buildFullPrompt(system: systemPrompt, context: context, prompt: prompt)

        let response = await callGeminiAPI(prompt: fullPrompt)
        return response
    }

    func generatePersonalizedResponse(
        studentName: String,
        gradeLevel: String,
        prompt: String,
        context: String
    ) async -> String {
        let personalizedPrompt = buildPersonalizedPrompt(
            studentName: studentName,
            gradeLevel: gradeLevel,
            prompt: prompt,
            context: context
        )

        return await generateResponse(
            prompt: personalizedPrompt,
            context: context,
            personality: nil
        )
    }

    // MARK: - Specialized AI Functions

    func generateLearningObjectives(
        studentName: String,
        gradeLevel: String,
        subject: String,
        currentLevel: String
    ) async -> [String] {
        let objectivePrompt = buildObjectiveGenerationPrompt(
            studentName: studentName,
            gradeLevel: gradeLevel,
            subject: subject,
            currentLevel: currentLevel
        )
        let response = await generateResponse(prompt: objectivePrompt, context: "")

        return parseObjectives(response)
    }

    func assessStudentUnderstanding(
        studentResponse: String,
        expectedAnswer: String,
        context: String
    ) async -> String {
        let assessmentPrompt = buildAssessmentPrompt(
            studentResponse: studentResponse,
            expectedAnswer: expectedAnswer,
            context: context
        )
        return await generateResponse(prompt: assessmentPrompt, context: "")
    }

    func generateAdaptiveContent(
        studentName: String,
        gradeLevel: String,
        topic: String,
        difficulty: String,
        learningStyle: String
    ) async -> String {
        let contentPrompt = buildAdaptiveContentPrompt(
            studentName: studentName,
            gradeLevel: gradeLevel,
            topic: topic,
            difficulty: difficulty,
            learningStyle: learningStyle
        )
        return await generateResponse(prompt: contentPrompt, context: "")
    }

    // MARK: - Emotional Intelligence

    func analyzeEmotionalState(
        studentInput: String,
        context: String
    ) async -> String {
        let emotionPrompt = buildEmotionAnalysisPrompt(input: studentInput, context: context)
        return await generateResponse(prompt: emotionPrompt, context: "")
    }

    func generateEmotionalSupport(
        studentName: String,
        emotionalState: String,
        context: String
    ) async -> String {
        let supportPrompt = buildEmotionalSupportPrompt(
            studentName: studentName,
            emotionalState: emotionalState,
            context: context
        )

        return await generateResponse(prompt: supportPrompt, context: context)
    }

    // MARK: - Private Helper Methods

    private func callGeminiAPI(prompt: String) async -> String {
        guard let url = URL(string: "\(baseURL)/models/gemini-2.0-flash-exp:generateContent?key=\(apiKey)") else {
            return "Error: Invalid API URL"
        }

        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ],
            generationConfig: GeminiGenerationConfig(
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048
            ),
            safetySettings: [
                GeminiSafetySettings(
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                ),
                GeminiSafetySettings(
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                )
            ]
        )

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let jsonData = try JSONEncoder().encode(requestBody)
            request.httpBody = jsonData

            let (data, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                return "Error: HTTP \(httpResponse.statusCode)"
            }

            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
            return geminiResponse.candidates.first?.content.parts.first?.text ?? "No response generated"

        } catch {
            return "Error generating response: \(error.localizedDescription)"
        }
    }

    private func buildSystemPrompt(personality: String?) -> String {
        guard let personality = personality else {
            return "You are a helpful AI teacher assistant."
        }

        var prompt = "You are an AI teacher with the following personality: \(personality)\n"
        prompt += "\nAlways respond in character with these personality traits. Be supportive, encouraging, and adaptive to the student's needs."

        return prompt
    }

    private func buildFullPrompt(system: String, context: String, prompt: String) -> String {
        var fullPrompt = system + "\n\n"

        if !context.isEmpty {
            fullPrompt += "Context: \(context)\n\n"
        }

        fullPrompt += "Student input: \(prompt)\n\n"
        fullPrompt += "Please respond as the AI teacher described above:"

        return fullPrompt
    }

    private func buildPersonalizedPrompt(
        studentName: String,
        gradeLevel: String,
        prompt: String,
        context: String
    ) -> String {
        var personalizedPrompt = "Student Profile:\n"
        personalizedPrompt += "- Name: \(studentName)\n"
        personalizedPrompt += "- Grade level: \(gradeLevel)\n\n"

        personalizedPrompt += "Current context: \(context)\n\n"
        personalizedPrompt += "Student says: \(prompt)\n\n"
        personalizedPrompt += "Respond in a way that's perfectly tailored to this specific student's needs, learning style, and current emotional state."

        return personalizedPrompt
    }

    private func parseObjectives(_ response: String) -> [String] {
        // Simple parsing - split by newlines and filter out empty lines
        return response.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
    }

    // Additional prompt building methods...
    private func buildObjectiveGenerationPrompt(
        studentName: String,
        gradeLevel: String,
        subject: String,
        currentLevel: String
    ) -> String {
        return "Generate 3-5 learning objectives for \(studentName) (Grade \(gradeLevel)) in \(subject) at \(currentLevel) level. List each objective on a new line."
    }

    private func buildAssessmentPrompt(studentResponse: String, expectedAnswer: String, context: String) -> String {
        return "Assess student response: '\(studentResponse)' against expected: '\(expectedAnswer)' in context: '\(context)'"
    }

    private func buildAdaptiveContentPrompt(
        studentName: String,
        gradeLevel: String,
        topic: String,
        difficulty: String,
        learningStyle: String
    ) -> String {
        return "Create adaptive content for \(studentName) (Grade \(gradeLevel)) on \(topic) at \(difficulty) difficulty for \(learningStyle) learner"
    }

    private func buildEmotionAnalysisPrompt(input: String, context: String) -> String {
        return "Analyze emotional state from: '\(input)' in context: '\(context)'"
    }

    private func buildEmotionalSupportPrompt(
        studentName: String,
        emotionalState: String,
        context: String
    ) -> String {
        return "Provide emotional support for \(studentName) who is feeling \(emotionalState) in context: '\(context)'"
    }
}

// MARK: - Gemini API Models

struct GeminiRequest: Codable {
    let contents: [GeminiContent]
    let generationConfig: GeminiGenerationConfig
    let safetySettings: [GeminiSafetySettings]
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String
}

struct GeminiGenerationConfig: Codable {
    let temperature: Double
    let topK: Int
    let topP: Double
    let maxOutputTokens: Int
}

struct GeminiSafetySettings: Codable {
    let category: String
    let threshold: String
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]
}

struct GeminiCandidate: Codable {
    let content: GeminiContent
}

// MARK: - Supporting Types

struct GeminiAssessmentResult {
    let score: Double
    let feedback: String
    let nextSteps: [String]
}

struct GeminiAdaptiveContent {
    let content: String
    let activities: [String]
    let assessments: [String]
}
