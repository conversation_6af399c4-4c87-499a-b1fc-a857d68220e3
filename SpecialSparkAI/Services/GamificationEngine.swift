//
//  GamificationEngine.swift
//  SpecialSparkAI
//
//  Phase 3: Gamification System Implementation
//

import Foundation
import SwiftData

// MARK: - Achievement System
@Model
class Achievement: Identifiable, Codable {
    @Attribute(.unique) var id: UUID
    var title: String
    var achievementDescription: String
    var category: GamificationAchievementCategory
    var difficulty: AchievementDifficulty
    var points: Int
    var iconName: String
    var badgeColor: String
    var requirements: [AchievementRequirement]
    var isHidden: Bool
    var isRepeatable: Bool
    var specialNeedsFriendly: Bool
    var createdAt: Date

    init(title: String, description: String, category: GamificationAchievementCategory, difficulty: AchievementDifficulty, points: Int, iconName: String = "star.fill", badgeColor: String = "#FFD700") {
        self.id = UUID()
        self.title = title
        self.achievementDescription = description
        self.category = category
        self.difficulty = difficulty
        self.points = points
        self.iconName = iconName
        self.badgeColor = badgeColor
        self.requirements = []
        self.isHidden = false
        self.isRepeatable = false
        self.specialNeedsFriendly = true
        self.createdAt = Date()
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, title, achievementDescription = "description", category, difficulty, points
        case iconName, badgeColor, requirements, isHidden, isRepeatable
        case specialNeedsFriendly, createdAt
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.title = try container.decode(String.self, forKey: .title)
        self.achievementDescription = try container.decode(String.self, forKey: .achievementDescription)
        self.category = try container.decode(GamificationAchievementCategory.self, forKey: .category)
        self.difficulty = try container.decode(AchievementDifficulty.self, forKey: .difficulty)
        self.points = try container.decode(Int.self, forKey: .points)
        self.iconName = try container.decode(String.self, forKey: .iconName)
        self.badgeColor = try container.decode(String.self, forKey: .badgeColor)
        self.requirements = try container.decode([AchievementRequirement].self, forKey: .requirements)
        self.isHidden = try container.decode(Bool.self, forKey: .isHidden)
        self.isRepeatable = try container.decode(Bool.self, forKey: .isRepeatable)
        self.specialNeedsFriendly = try container.decode(Bool.self, forKey: .specialNeedsFriendly)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(achievementDescription, forKey: .achievementDescription)
        try container.encode(category, forKey: .category)
        try container.encode(difficulty, forKey: .difficulty)
        try container.encode(points, forKey: .points)
        try container.encode(iconName, forKey: .iconName)
        try container.encode(badgeColor, forKey: .badgeColor)
        try container.encode(requirements, forKey: .requirements)
        try container.encode(isHidden, forKey: .isHidden)
        try container.encode(isRepeatable, forKey: .isRepeatable)
        try container.encode(specialNeedsFriendly, forKey: .specialNeedsFriendly)
        try container.encode(createdAt, forKey: .createdAt)
    }
}

enum GamificationAchievementCategory: String, CaseIterable, Codable {
    case learning = "Learning"
    case social = "Social"
    case creativity = "Creativity"
    case persistence = "Persistence"
    case kindness = "Kindness"
    case exploration = "Exploration"
    case milestone = "Milestone"
    case special = "Special"
}

enum AchievementDifficulty: String, CaseIterable, Codable {
    case bronze = "Bronze"
    case silver = "Silver"
    case gold = "Gold"
    case platinum = "Platinum"
    case diamond = "Diamond"

    var points: Int {
        switch self {
        case .bronze: return 10
        case .silver: return 25
        case .gold: return 50
        case .platinum: return 100
        case .diamond: return 200
        }
    }

    var color: String {
        switch self {
        case .bronze: return "#CD7F32"
        case .silver: return "#C0C0C0"
        case .gold: return "#FFD700"
        case .platinum: return "#E5E4E2"
        case .diamond: return "#B9F2FF"
        }
    }
}

struct AchievementRequirement: Identifiable, Codable {
    let id = UUID()
    let type: RequirementType
    let target: Int
    let description: String

    enum RequirementType: String, CaseIterable, Codable {
        case conversationsCompleted = "conversations_completed"
        case questionsAnswered = "questions_answered"
        case correctAnswers = "correct_answers"
        case streakDays = "streak_days"
        case timeSpent = "time_spent"
        case subjectsExplored = "subjects_explored"
        case helpRequests = "help_requests"
        case encouragementGiven = "encouragement_given"
    }
}

// MARK: - Student Progress Tracking
@Model
class StudentProgress: Identifiable, Codable {
    @Attribute(.unique) var id: UUID
    var studentId: UUID
    var totalPoints: Int
    var currentLevel: Int
    var experiencePoints: Int
    var pointsToNextLevel: Int
    var achievementsUnlocked: [UUID]
    var badges: [GamificationBadge]
    var streakDays: Int
    var lastActivityDate: Date
    var statistics: ProgressStatistics
    var rewards: [Reward]

    init(studentId: UUID) {
        self.id = UUID()
        self.studentId = studentId
        self.totalPoints = 0
        self.currentLevel = 1
        self.experiencePoints = 0
        self.pointsToNextLevel = 100
        self.achievementsUnlocked = []
        self.badges = []
        self.streakDays = 0
        self.lastActivityDate = Date()
        self.statistics = ProgressStatistics()
        self.rewards = []
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, studentId, totalPoints, currentLevel, experiencePoints
        case pointsToNextLevel, achievementsUnlocked, badges, streakDays
        case lastActivityDate, statistics, rewards
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.studentId = try container.decode(UUID.self, forKey: .studentId)
        self.totalPoints = try container.decode(Int.self, forKey: .totalPoints)
        self.currentLevel = try container.decode(Int.self, forKey: .currentLevel)
        self.experiencePoints = try container.decode(Int.self, forKey: .experiencePoints)
        self.pointsToNextLevel = try container.decode(Int.self, forKey: .pointsToNextLevel)
        self.achievementsUnlocked = try container.decode([UUID].self, forKey: .achievementsUnlocked)
        self.badges = try container.decode([GamificationBadge].self, forKey: .badges)
        self.streakDays = try container.decode(Int.self, forKey: .streakDays)
        self.lastActivityDate = try container.decode(Date.self, forKey: .lastActivityDate)
        self.statistics = try container.decode(ProgressStatistics.self, forKey: .statistics)
        self.rewards = try container.decode([Reward].self, forKey: .rewards)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(studentId, forKey: .studentId)
        try container.encode(totalPoints, forKey: .totalPoints)
        try container.encode(currentLevel, forKey: .currentLevel)
        try container.encode(experiencePoints, forKey: .experiencePoints)
        try container.encode(pointsToNextLevel, forKey: .pointsToNextLevel)
        try container.encode(achievementsUnlocked, forKey: .achievementsUnlocked)
        try container.encode(badges, forKey: .badges)
        try container.encode(streakDays, forKey: .streakDays)
        try container.encode(lastActivityDate, forKey: .lastActivityDate)
        try container.encode(statistics, forKey: .statistics)
        try container.encode(rewards, forKey: .rewards)
    }
}

struct GamificationBadge: Identifiable, Codable {
    var id = UUID()
    let achievementId: UUID
    let earnedAt: Date
    let title: String
    let iconName: String
    let color: String

    init(achievementId: UUID, earnedAt: Date, title: String, iconName: String, color: String) {
        self.id = UUID()
        self.achievementId = achievementId
        self.earnedAt = earnedAt
        self.title = title
        self.iconName = iconName
        self.color = color
    }
}

struct ProgressStatistics: Codable {
    var conversationsCompleted: Int = 0
    var questionsAnswered: Int = 0
    var correctAnswers: Int = 0
    var totalTimeSpent: TimeInterval = 0
    var subjectsExplored: Set<String> = []
    var helpRequestsMade: Int = 0
    var encouragementReceived: Int = 0
    var averageSessionTime: TimeInterval = 0
    var bestStreak: Int = 0
    var favoriteSubject: String = ""

    var accuracy: Double {
        guard questionsAnswered > 0 else { return 0 }
        return Double(correctAnswers) / Double(questionsAnswered) * 100
    }
}

struct Reward: Identifiable, Codable {
    let id = UUID()
    let type: RewardType
    let title: String
    let description: String
    let iconName: String
    let isUnlocked: Bool
    let unlockedAt: Date?

    enum RewardType: String, CaseIterable, Codable {
        case avatar = "Avatar"
        case theme = "Theme"
        case sticker = "Sticker"
        case certificate = "Certificate"
        case privilege = "Privilege"
    }
}

// MARK: - Gamification Engine
class GamificationEngine: ObservableObject {
    static let shared = GamificationEngine()

    @Published var studentProgress: StudentProgress?
    @Published var availableAchievements: [Achievement] = []
    @Published var recentlyUnlocked: [Achievement] = []
    @Published var isLoading = false

    private let supabaseService = SupabaseService.shared

    private init() {
        setupDefaultAchievements()
    }

    // MARK: - Progress Management
    func loadProgress(for student: Student) async {
        await MainActor.run {
            self.isLoading = true
        }

        // Try to load existing progress
        if let existingProgress = await loadStudentProgress(studentId: student.id) {
            await MainActor.run {
                self.studentProgress = existingProgress
                self.isLoading = false
            }
        } else {
            // Create new progress
            let newProgress = StudentProgress(studentId: student.id)
            await saveStudentProgress(newProgress)

            await MainActor.run {
                self.studentProgress = newProgress
                self.isLoading = false
            }
        }
    }

    func awardPoints(_ points: Int, for activity: String, student: Student) async {
        guard let progress = studentProgress else { return }

        progress.totalPoints += points
        progress.experiencePoints += points
        progress.lastActivityDate = Date()

        // Check for level up
        await checkLevelUp(progress)

        // Check for achievements
        await checkAchievements(progress, activity: activity)

        // Update streak
        await updateStreak(progress)

        // Save progress
        await saveStudentProgress(progress)

        await MainActor.run {
            self.studentProgress = progress
        }
    }

    private func checkLevelUp(_ progress: StudentProgress) async {
        let pointsNeeded = calculatePointsForLevel(progress.currentLevel + 1)

        if progress.experiencePoints >= pointsNeeded {
            progress.currentLevel += 1
            progress.pointsToNextLevel = calculatePointsForLevel(progress.currentLevel + 1) - progress.experiencePoints

            // Award level up achievement
            await unlockAchievement(title: "Level Up!", for: progress)
        } else {
            progress.pointsToNextLevel = pointsNeeded - progress.experiencePoints
        }
    }

    private func calculatePointsForLevel(_ level: Int) -> Int {
        return level * 100 + (level - 1) * 50 // Exponential growth
    }

    private func checkAchievements(_ progress: StudentProgress, activity: String) async {
        for achievement in availableAchievements {
            if !progress.achievementsUnlocked.contains(achievement.id) {
                if await meetsRequirements(achievement, progress: progress) {
                    await unlockAchievement(achievement, for: progress)
                }
            }
        }
    }

    private func meetsRequirements(_ achievement: Achievement, progress: StudentProgress) async -> Bool {
        for requirement in achievement.requirements {
            switch requirement.type {
            case .conversationsCompleted:
                if progress.statistics.conversationsCompleted < requirement.target { return false }
            case .questionsAnswered:
                if progress.statistics.questionsAnswered < requirement.target { return false }
            case .correctAnswers:
                if progress.statistics.correctAnswers < requirement.target { return false }
            case .streakDays:
                if progress.streakDays < requirement.target { return false }
            case .timeSpent:
                if Int(progress.statistics.totalTimeSpent / 60) < requirement.target { return false }
            case .subjectsExplored:
                if progress.statistics.subjectsExplored.count < requirement.target { return false }
            case .helpRequests:
                if progress.statistics.helpRequestsMade < requirement.target { return false }
            case .encouragementGiven:
                if progress.statistics.encouragementReceived < requirement.target { return false }
            }
        }
        return true
    }

    private func unlockAchievement(_ achievement: Achievement, for progress: StudentProgress) async {
        progress.achievementsUnlocked.append(achievement.id)
        progress.totalPoints += achievement.points
        progress.experiencePoints += achievement.points

        let badge = GamificationBadge(
            achievementId: achievement.id,
            earnedAt: Date(),
            title: achievement.title,
            iconName: achievement.iconName,
            color: achievement.badgeColor
        )
        progress.badges.append(badge)

        await MainActor.run {
            self.recentlyUnlocked.append(achievement)
        }
    }

    private func unlockAchievement(title: String, for progress: StudentProgress) async {
        if let achievement = availableAchievements.first(where: { $0.title == title }) {
            await unlockAchievement(achievement, for: progress)
        }
    }

    private func updateStreak(_ progress: StudentProgress) async {
        let calendar = Calendar.current
        let today = Date()

        if calendar.isDate(progress.lastActivityDate, inSameDayAs: today) {
            // Same day, no change
            return
        } else if calendar.isDate(progress.lastActivityDate, inSameDayAs: calendar.date(byAdding: .day, value: -1, to: today)!) {
            // Yesterday, continue streak
            progress.streakDays += 1
            if progress.streakDays > progress.statistics.bestStreak {
                progress.statistics.bestStreak = progress.streakDays
            }
        } else {
            // Streak broken
            progress.streakDays = 1
        }
    }

    // MARK: - Default Achievements Setup
    private func setupDefaultAchievements() {
        availableAchievements = [
            // Learning Achievements
            Achievement(
                title: "First Steps",
                description: "Complete your first conversation with an AI teacher",
                category: .learning,
                difficulty: .bronze,
                points: 10,
                iconName: "figure.walk",
                badgeColor: "#CD7F32"
            ),
            Achievement(
                title: "Curious Mind",
                description: "Ask 10 questions to your AI teachers",
                category: .learning,
                difficulty: .silver,
                points: 25,
                iconName: "questionmark.circle.fill",
                badgeColor: "#C0C0C0"
            ),
            Achievement(
                title: "Knowledge Seeker",
                description: "Answer 50 questions correctly",
                category: .learning,
                difficulty: .gold,
                points: 50,
                iconName: "brain.head.profile",
                badgeColor: "#FFD700"
            ),
            Achievement(
                title: "Subject Explorer",
                description: "Try learning in 5 different subjects",
                category: .exploration,
                difficulty: .silver,
                points: 30,
                iconName: "map.fill",
                badgeColor: "#C0C0C0"
            ),
            Achievement(
                title: "Persistent Learner",
                description: "Maintain a 7-day learning streak",
                category: .persistence,
                difficulty: .gold,
                points: 75,
                iconName: "flame.fill",
                badgeColor: "#FFD700"
            ),
            Achievement(
                title: "Helper",
                description: "Ask for help when you need it 5 times",
                category: .social,
                difficulty: .bronze,
                points: 15,
                iconName: "hand.raised.fill",
                badgeColor: "#CD7F32"
            ),
            Achievement(
                title: "Creative Thinker",
                description: "Spend 2 hours in creative subjects",
                category: .creativity,
                difficulty: .silver,
                points: 40,
                iconName: "paintbrush.fill",
                badgeColor: "#C0C0C0"
            ),
            Achievement(
                title: "Kind Heart",
                description: "Receive encouragement from teachers 20 times",
                category: .kindness,
                difficulty: .gold,
                points: 60,
                iconName: "heart.fill",
                badgeColor: "#FFD700"
            ),
            Achievement(
                title: "Level Master",
                description: "Reach level 10",
                category: .learning,
                difficulty: .platinum,
                points: 100,
                iconName: "crown.fill",
                badgeColor: "#E5E4E2"
            ),
            Achievement(
                title: "Special Star",
                description: "Complete 100 learning activities",
                category: .learning,
                difficulty: .diamond,
                points: 200,
                iconName: "star.circle.fill",
                badgeColor: "#B9F2FF"
            )
        ]

        // Add requirements to achievements
        setupAchievementRequirements()
    }

    private func setupAchievementRequirements() {
        for achievement in availableAchievements {
            switch achievement.title {
            case "First Steps":
                achievement.requirements = [
                    AchievementRequirement(type: .conversationsCompleted, target: 1, description: "Complete 1 conversation")
                ]
            case "Curious Mind":
                achievement.requirements = [
                    AchievementRequirement(type: .questionsAnswered, target: 10, description: "Answer 10 questions")
                ]
            case "Knowledge Seeker":
                achievement.requirements = [
                    AchievementRequirement(type: .correctAnswers, target: 50, description: "Get 50 answers correct")
                ]
            case "Subject Explorer":
                achievement.requirements = [
                    AchievementRequirement(type: .subjectsExplored, target: 5, description: "Explore 5 subjects")
                ]
            case "Persistent Learner":
                achievement.requirements = [
                    AchievementRequirement(type: .streakDays, target: 7, description: "7-day streak")
                ]
            case "Helper":
                achievement.requirements = [
                    AchievementRequirement(type: .helpRequests, target: 5, description: "Ask for help 5 times")
                ]
            case "Creative Thinker":
                achievement.requirements = [
                    AchievementRequirement(type: .timeSpent, target: 120, description: "Spend 2 hours learning")
                ]
            case "Kind Heart":
                achievement.requirements = [
                    AchievementRequirement(type: .encouragementGiven, target: 20, description: "Receive 20 encouragements")
                ]
            default:
                break
            }
        }
    }

    // MARK: - Data Persistence
    private func loadStudentProgress(studentId: UUID) async -> StudentProgress? {
        // In a real app, load from Supabase
        return nil
    }

    private func saveStudentProgress(_ progress: StudentProgress) async {
        // In a real app, save to Supabase
        if !APIConfig.Development.useMockData {
            // await supabaseService.saveStudentProgress(progress)
        }
    }
}
