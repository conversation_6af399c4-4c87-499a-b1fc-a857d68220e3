//
//  AdaptiveLearningService.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import Foundation
import SwiftUI
import Supabase

// MARK: - Adaptive Learning Service
class AdaptiveLearningService: ObservableObject {
    static let shared = AdaptiveLearningService()

    @Published var currentRecommendations: [AdaptiveLearningRecommendation] = []
    @Published var learningProgress: [LearningProgress] = []
    @Published var isLoading = false
    @Published var error: String?

    private let supabaseService = SupabaseService.shared
    private let geminiService = GeminiService()

    private init() {}

    // MARK: - Core Adaptive Learning Functions

    /// Analyzes student performance and generates personalized recommendations
    func generateAdaptiveRecommendations(for studentId: UUID) async {
        await MainActor.run { isLoading = true }

        do {
            // Get student's recent learning data
            let recentSessions = try await fetchRecentLearningSessions(studentId: studentId)
            let progressData = try await fetchLearningProgress(studentId: studentId)
            let emotionalData = try await fetchEmotionalStateHistory(studentId: studentId)
            let assessmentData = try await fetchRecentAssessments(studentId: studentId)

            // Analyze patterns using AI
            let analysisResult = await analyzeStudentPatterns(
                sessions: recentSessions,
                progress: progressData,
                emotions: emotionalData,
                assessments: assessmentData
            )

            // Generate recommendations
            let recommendations = await generateRecommendations(
                studentId: studentId,
                analysis: analysisResult
            )

            // Save recommendations to database
            try await saveRecommendations(recommendations)

            await MainActor.run {
                self.currentRecommendations = recommendations
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    /// Adapts difficulty level based on student performance
    func adaptDifficultyLevel(
        studentId: UUID,
        subjectId: UUID,
        currentPerformance: Double,
        responseTime: Double,
        emotionalState: String
    ) async -> String {

        // Get historical performance
        let historicalData = await getHistoricalPerformance(
            studentId: studentId,
            subjectId: subjectId
        )

        // AI-powered difficulty adjustment
        let prompt = buildDifficultyAdjustmentPrompt(
            currentPerformance: currentPerformance,
            responseTime: responseTime,
            emotionalState: emotionalState,
            historicalData: historicalData
        )

        let aiRecommendation = await geminiService.generateResponse(
            prompt: prompt,
            context: "Difficulty adjustment for adaptive learning"
        )

        return parseDifficultyRecommendation(aiRecommendation)
    }

    /// Personalizes teaching approach based on learning style and special needs
    func personalizeTeachingApproach(
        for studentProfile: StudentProfile,
        subject: String,
        currentTopic: String
    ) async -> TeachingPersonalization {

        let prompt = buildPersonalizationPrompt(
            profile: studentProfile,
            subject: subject,
            topic: currentTopic
        )

        let aiResponse = await geminiService.generateResponse(
            prompt: prompt,
            context: "Teaching personalization for special needs student"
        )

        return parsePersonalizationResponse(aiResponse)
    }

    /// Tracks and analyzes emotional state during learning
    func trackEmotionalState(
        studentId: UUID,
        sessionId: UUID?,
        indicators: EmotionalIndicators
    ) async {

        let emotionalRecord = EmotionalStateRecord(
            id: UUID(),
            studentId: studentId,
            sessionId: sessionId,
            emotionalState: indicators.primaryState,
            confidence: indicators.confidence,
            engagement: indicators.engagement,
            frustration: indicators.frustration,
            excitement: indicators.excitement,
            context: indicators.context,
            triggers: indicators.triggers,
            interventions: [],
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        do {
            try await supabaseService.client
                .from("emotional_state_records")
                .insert(emotionalRecord)
                .execute()

            // Generate interventions if needed
            if indicators.frustration > 0.7 || indicators.engagement < 0.3 {
                await generateEmotionalInterventions(
                    studentId: studentId,
                    emotionalState: indicators.primaryState
                )
            }

        } catch {
            print("❌ Error tracking emotional state: \(error)")
        }
    }

    /// Updates learning progress based on assessment results
    func updateLearningProgress(
        studentId: UUID,
        subjectId: UUID,
        skillId: UUID,
        assessmentResult: AssessmentRecord
    ) async {

        do {
            // Get current progress
            let currentProgress = try await fetchSkillProgress(
                studentId: studentId,
                subjectId: subjectId,
                skillId: skillId
            )

            // Calculate new mastery score
            let newMasteryScore = calculateMasteryScore(
                currentScore: currentProgress?.masteryScore ?? 0.0,
                assessmentResult: assessmentResult
            )

            // Determine next level and recommendations
            let (nextLevel, recommendations) = await determineNextSteps(
                currentLevel: currentProgress?.currentLevel ?? "Beginner",
                masteryScore: newMasteryScore,
                studentProfile: try await fetchStudentProfile(studentId)
            )

            // Update progress record
            let updatedProgress = LearningProgress(
                id: currentProgress?.id ?? UUID(),
                studentId: studentId,
                subjectId: subjectId,
                skillId: skillId,
                currentLevel: nextLevel,
                masteryScore: newMasteryScore,
                attemptsCount: (currentProgress?.attemptsCount ?? 0) + 1,
                lastAttemptDate: ISO8601DateFormatter().string(from: Date()),
                strengthAreas: identifyStrengths(assessmentResult),
                improvementAreas: identifyImprovements(assessmentResult),
                nextRecommendations: recommendations,
                adaptivePath: generateAdaptivePath(newMasteryScore, nextLevel),
                createdAt: currentProgress?.createdAt ?? ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            )

            // Save to database
            try await supabaseService.client
                .from("learning_progress")
                .upsert(updatedProgress)
                .execute()

        } catch {
            print("❌ Error updating learning progress: \(error)")
        }
    }

    // MARK: - AI-Powered Analysis Functions

    private func analyzeStudentPatterns(
        sessions: [LearningSessionRecord],
        progress: [LearningProgress],
        emotions: [EmotionalStateRecord],
        assessments: [AssessmentRecord]
    ) async -> StudentAnalysis {

        let prompt = buildAnalysisPrompt(
            sessions: sessions,
            progress: progress,
            emotions: emotions,
            assessments: assessments
        )

        let aiAnalysis = await geminiService.generateResponse(
            prompt: prompt,
            context: "Comprehensive student learning analysis"
        )

        return parseStudentAnalysis(aiAnalysis)
    }

    private func generateRecommendations(
        studentId: UUID,
        analysis: StudentAnalysis
    ) async -> [AdaptiveLearningRecommendation] {

        let prompt = buildRecommendationPrompt(analysis: analysis)

        let aiRecommendations = await geminiService.generateResponse(
            prompt: prompt,
            context: "Adaptive learning recommendations generation"
        )

        return parseRecommendations(aiRecommendations, studentId: studentId)
    }

    private func generateEmotionalInterventions(
        studentId: UUID,
        emotionalState: String
    ) async {

        let prompt = buildInterventionPrompt(emotionalState: emotionalState)

        let interventions = await geminiService.generateResponse(
            prompt: prompt,
            context: "Emotional support interventions"
        )

        // Apply interventions (this would trigger UI changes, teacher notifications, etc.)
        await applyInterventions(studentId: studentId, interventions: interventions)
    }

    // MARK: - Database Operations

    private func fetchRecentLearningSessions(studentId: UUID) async throws -> [LearningSessionRecord] {
        let response = try await supabaseService.client
            .from("learning_sessions")
            .select()
            .eq("student_id", value: studentId.uuidString)
            .gte("start_time", value: Calendar.current.date(byAdding: .day, value: -30, to: Date())!)
            .order("start_time", ascending: false)
            .limit(50)
            .execute()

        return try response.decoded()
    }

    private func fetchLearningProgress(studentId: UUID) async throws -> [LearningProgress] {
        let response = try await supabaseService.client
            .from("learning_progress")
            .select()
            .eq("student_id", value: studentId.uuidString)
            .execute()

        return try response.decoded()
    }

    private func fetchEmotionalStateHistory(studentId: UUID) async throws -> [EmotionalStateRecord] {
        let response = try await supabaseService.client
            .from("emotional_state_records")
            .select()
            .eq("student_id", value: studentId.uuidString)
            .gte("created_at", value: Calendar.current.date(byAdding: .day, value: -14, to: Date())!)
            .order("created_at", ascending: false)
            .execute()

        return try response.decoded()
    }

    private func fetchRecentAssessments(studentId: UUID) async throws -> [AssessmentRecord] {
        let response = try await supabaseService.client
            .from("assessment_records")
            .select()
            .eq("student_id", value: studentId.uuidString)
            .gte("created_at", value: Calendar.current.date(byAdding: .day, value: -7, to: Date())!)
            .order("created_at", ascending: false)
            .limit(100)
            .execute()

        return try response.decoded()
    }

    private func fetchStudentProfile(_ studentId: UUID) async throws -> StudentProfile {
        let response = try await supabaseService.client
            .from("student_profiles")
            .select()
            .eq("id", value: studentId.uuidString)
            .single()
            .execute()

        return try response.decoded()
    }

    private func saveRecommendations(_ recommendations: [AdaptiveLearningRecommendation]) async throws {
        try await supabaseService.client
            .from("adaptive_learning_recommendations")
            .insert(recommendations)
            .execute()
    }

    // MARK: - Helper Functions

    private func calculateMasteryScore(
        currentScore: Double,
        assessmentResult: AssessmentRecord
    ) -> Double {
        let weight = 0.3 // Weight for new assessment
        let newScore = assessmentResult.isCorrect ? 1.0 : 0.0
        return (currentScore * (1 - weight)) + (newScore * weight)
    }

    private func identifyStrengths(_ assessment: AssessmentRecord) -> [String] {
        // AI-powered strength identification would go here
        return assessment.isCorrect ? [assessment.skillTested] : []
    }

    private func identifyImprovements(_ assessment: AssessmentRecord) -> [String] {
        // AI-powered improvement identification would go here
        return assessment.isCorrect ? [] : [assessment.skillTested]
    }

    private func generateAdaptivePath(_ masteryScore: Double, _ level: String) -> String {
        if masteryScore >= 0.8 {
            return "accelerated"
        } else if masteryScore >= 0.6 {
            return "standard"
        } else {
            return "reinforcement"
        }
    }

    // MARK: - Prompt Building Functions

    private func buildAnalysisPrompt(
        sessions: [LearningSessionRecord],
        progress: [LearningProgress],
        emotions: [EmotionalStateRecord],
        assessments: [AssessmentRecord]
    ) -> String {
        return """
        Analyze this student's learning data and provide insights:

        Recent Sessions: \(sessions.count) sessions
        Average Completion Rate: \(sessions.map { $0.completionRate }.reduce(0, +) / Double(sessions.count))
        Average Engagement: \(sessions.map { $0.engagementScore }.reduce(0, +) / Double(sessions.count))

        Progress Data: \(progress.count) skills tracked
        Emotional States: \(emotions.map { $0.emotionalState }.joined(separator: ", "))

        Assessment Performance: \(assessments.filter { $0.isCorrect }.count)/\(assessments.count) correct

        Provide analysis in JSON format with:
        - strengths: [string]
        - challenges: [string]
        - learningStyle: string
        - recommendedAdjustments: [string]
        - emotionalSupport: [string]
        """
    }

    private func buildRecommendationPrompt(analysis: StudentAnalysis) -> String {
        return """
        Based on this student analysis, generate 3-5 adaptive learning recommendations:

        Strengths: \(analysis.strengths.joined(separator: ", "))
        Challenges: \(analysis.challenges.joined(separator: ", "))
        Learning Style: \(analysis.learningStyle)

        Generate recommendations in JSON format with:
        - type: string (content, difficulty, pace, support)
        - priority: string (high, medium, low)
        - title: string
        - description: string
        - actionItems: [string]
        - estimatedDuration: number (minutes)
        """
    }

    private func buildPersonalizationPrompt(
        profile: StudentProfile,
        subject: String,
        topic: String
    ) -> String {
        return """
        Personalize teaching approach for this student:

        Student: \(profile.firstName), Grade \(profile.gradeLevel)
        Special Needs: \(profile.specialNeeds.joined(separator: ", "))
        Learning Style: \(profile.learningStyle)
        Subject: \(subject)
        Topic: \(topic)

        Provide personalization in JSON format with:
        - teachingStyle: string
        - visualAids: [string]
        - pacing: string
        - supportStrategies: [string]
        - assessmentAdaptations: [string]
        """
    }

    private func buildDifficultyAdjustmentPrompt(
        currentPerformance: Double,
        responseTime: Double,
        emotionalState: String,
        historicalData: [Double]
    ) -> String {
        return """
        Recommend difficulty adjustment:

        Current Performance: \(currentPerformance)
        Response Time: \(responseTime) seconds
        Emotional State: \(emotionalState)
        Historical Performance: \(historicalData)

        Recommend one of: easier, maintain, harder
        """
    }

    private func buildInterventionPrompt(emotionalState: String) -> String {
        return """
        Generate emotional support interventions for a student showing: \(emotionalState)

        Provide interventions as JSON array of strings with specific, actionable support strategies.
        """
    }

    // MARK: - Response Parsing Functions

    private func parseStudentAnalysis(_ response: String) -> StudentAnalysis {
        // Parse AI response into structured analysis
        return StudentAnalysis(
            strengths: ["Problem solving", "Visual learning"],
            challenges: ["Reading comprehension", "Time management"],
            learningStyle: "visual",
            recommendedAdjustments: ["More visual aids", "Shorter sessions"],
            emotionalSupport: ["Frequent encouragement", "Break reminders"]
        )
    }

    private func parseRecommendations(_ response: String, studentId: UUID) -> [AdaptiveLearningRecommendation] {
        // Parse AI response into recommendations
        return [
            AdaptiveLearningRecommendation(
                id: UUID(),
                studentId: studentId,
                recommendationType: "content",
                priority: "high",
                title: "Visual Math Activities",
                description: "Increase visual elements in math lessons",
                actionItems: ["Use manipulatives", "Add diagrams", "Color-code steps"],
                estimatedDuration: 30,
                difficultyAdjustment: nil,
                personalityAdjustment: nil,
                isCompleted: false,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                completedAt: nil
            )
        ]
    }

    private func parsePersonalizationResponse(_ response: String) -> TeachingPersonalization {
        return TeachingPersonalization(
            teachingStyle: "visual-kinesthetic",
            visualAids: ["Charts", "Diagrams", "Videos"],
            pacing: "moderate",
            supportStrategies: ["Frequent breaks", "Positive reinforcement"],
            assessmentAdaptations: ["Extended time", "Visual cues"]
        )
    }

    private func parseDifficultyRecommendation(_ response: String) -> String {
        if response.lowercased().contains("easier") {
            return "easier"
        } else if response.lowercased().contains("harder") {
            return "harder"
        } else {
            return "maintain"
        }
    }

    private func applyInterventions(studentId: UUID, interventions: String) async {
        // Apply emotional support interventions
        print("🎯 Applying interventions for student \(studentId): \(interventions)")
    }

    private func getHistoricalPerformance(studentId: UUID, subjectId: UUID) async -> [Double] {
        // Fetch historical performance data
        return [0.7, 0.8, 0.6, 0.9, 0.7]
    }

    private func fetchSkillProgress(
        studentId: UUID,
        subjectId: UUID,
        skillId: UUID
    ) async throws -> LearningProgress? {
        let response = try await supabaseService.client
            .from("learning_progress")
            .select()
            .eq("student_id", value: studentId.uuidString)
            .eq("subject_id", value: subjectId.uuidString)
            .eq("skill_id", value: skillId.uuidString)
            .maybeSingle()
            .execute()

        return try response.decoded()
    }

    private func determineNextSteps(
        currentLevel: String,
        masteryScore: Double,
        studentProfile: StudentProfile
    ) async -> (String, [String]) {
        let nextLevel = masteryScore >= 0.8 ? "Advanced" : currentLevel
        let recommendations = [
            "Continue practicing current skills",
            "Focus on areas needing improvement"
        ]
        return (nextLevel, recommendations)
    }
}

// MARK: - Supporting Models

struct StudentAnalysis {
    let strengths: [String]
    let challenges: [String]
    let learningStyle: String
    let recommendedAdjustments: [String]
    let emotionalSupport: [String]
}

struct TeachingPersonalization {
    let teachingStyle: String
    let visualAids: [String]
    let pacing: String
    let supportStrategies: [String]
    let assessmentAdaptations: [String]
}

struct EmotionalIndicators {
    let primaryState: String
    let confidence: Double
    let engagement: Double
    let frustration: Double
    let excitement: Double
    let context: String
    let triggers: [String]
}
