import Foundation
import SwiftData

// MARK: - Subject Data Service
// Provides complete K-12 curriculum subjects

class SubjectDataService {
    static let shared = SubjectDataService()

    private init() {}

    // MARK: - Get Subjects by Grade Level

    func getSubjectsFor(gradeLevel: GradeLevel) -> [Subject] {
        return allSubjects.filter { subject in
            subject.isAvailableFor(gradeLevel: gradeLevel)
        }
    }

    func getSubjectsFor(schoolLevel: SchoolLevel) -> [Subject] {
        return allSubjects.filter { subject in
            subject.isAvailableFor(schoolLevel: schoolLevel)
        }
    }

    func getCoreSubjectsFor(gradeLevel: GradeLevel) -> [Subject] {
        return getSubjectsFor(gradeLevel: gradeLevel).filter { $0.category == .core }
    }

    func getAPSubjectsFor(gradeLevel: GradeLevel) -> [Subject] {
        return getSubjectsFor(gradeLevel: gradeLevel).filter { $0.isAP }
    }

    func getSpecialNeedsSubjects() -> [Subject] {
        return allSubjects.filter { $0.category == .specialNeeds }
    }

    // MARK: - Complete K-12 Subject Catalog

    lazy var allSubjects: [Subject] = [
        // MARK: - Elementary Subjects (K-5)

        // Core Elementary
        Subject(name: "Reading & Language Arts", code: "ELA_ELEM", category: .core,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Foundational reading, writing, and language skills"),

        Subject(name: "Mathematics", code: "MATH_ELEM", category: .core,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Basic math concepts and problem solving"),

        Subject(name: "Science", code: "SCI_ELEM", category: .stem,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Introduction to scientific concepts and inquiry"),

        Subject(name: "Social Studies", code: "SS_ELEM", category: .socialStudies,
                schoolLevels: [.elementary], gradeLevels: [.kindergarten, .grade1, .grade2, .grade3, .grade4, .grade5],
                description: "Community, geography, and basic history"),

        // MARK: - Middle School Subjects (6-8)

        // Core Middle School
        Subject(name: "English Language Arts", code: "ELA_MID", category: .core,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7, .grade8],
                description: "Advanced reading, writing, and literature"),

        Subject(name: "Pre-Algebra", code: "PREALG", category: .core,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7],
                description: "Introduction to algebraic concepts"),

        Subject(name: "Algebra I", code: "ALG1", category: .core,
                schoolLevels: [.middle, .high], gradeLevels: [.grade8, .grade9],
                description: "Linear equations and functions"),

        Subject(name: "Life Science", code: "LIFESCI", category: .stem,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7],
                description: "Biology basics and life processes"),

        Subject(name: "Physical Science", code: "PHYSCI", category: .stem,
                schoolLevels: [.middle], gradeLevels: [.grade8],
                description: "Introduction to chemistry and physics"),

        Subject(name: "World History", code: "WHIST_MID", category: .socialStudies,
                schoolLevels: [.middle], gradeLevels: [.grade6, .grade7],
                description: "Ancient and medieval civilizations"),

        Subject(name: "US History", code: "USHIST_MID", category: .socialStudies,
                schoolLevels: [.middle], gradeLevels: [.grade8],
                description: "American history through Civil War"),

        // MARK: - High School Core Subjects (9-12)

        // English
        Subject(name: "English 9", code: "ENG9", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade9],
                description: "Literature and composition"),

        Subject(name: "English 10", code: "ENG10", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade10],
                description: "World literature and writing"),

        Subject(name: "English 11", code: "ENG11", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade11],
                description: "American literature"),

        Subject(name: "English 12", code: "ENG12", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "British literature and college prep"),

        // Mathematics
        Subject(name: "Geometry", code: "GEOM", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Geometric proofs and spatial reasoning"),

        Subject(name: "Algebra II", code: "ALG2", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "Advanced algebraic concepts"),

        Subject(name: "Pre-Calculus", code: "PRECALC", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Trigonometry and advanced functions"),

        Subject(name: "Statistics", code: "STATS", category: .core,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Data analysis and probability"),

        // Sciences
        Subject(name: "Biology", code: "BIO", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade9, .grade10],
                description: "Cellular biology and genetics"),

        Subject(name: "Chemistry", code: "CHEM", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "Chemical reactions and bonding"),

        Subject(name: "Physics", code: "PHYS", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Mechanics and electromagnetic theory"),

        Subject(name: "Environmental Science", code: "ENVSCI", category: .stem,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Ecology and environmental systems"),

        // Social Studies
        Subject(name: "World History", code: "WHIST_HIGH", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade9],
                description: "Global civilizations and cultures"),

        Subject(name: "US History", code: "USHIST_HIGH", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "American history and government"),

        Subject(name: "Government", code: "GOV", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "Civics and political systems"),

        Subject(name: "Economics", code: "ECON", category: .socialStudies,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "Economic principles and systems"),

        // MARK: - Advanced Placement (AP) Subjects

        Subject(name: "AP English Literature", code: "AP_ENGLIT", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level literature analysis", isAP: true),

        Subject(name: "AP English Language", code: "AP_ENGLANG", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level composition and rhetoric", isAP: true),

        Subject(name: "AP Calculus AB", code: "AP_CALCAB", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Differential and integral calculus", isAP: true),

        Subject(name: "AP Calculus BC", code: "AP_CALCBC", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade12],
                description: "Advanced calculus concepts", isAP: true),

        Subject(name: "AP Biology", code: "AP_BIO", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level biology", isAP: true),

        Subject(name: "AP Chemistry", code: "AP_CHEM", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "College-level chemistry", isAP: true),

        Subject(name: "AP Physics 1", code: "AP_PHYS1", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Algebra-based physics", isAP: true),

        Subject(name: "AP US History", code: "AP_USHIST", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "College-level American history", isAP: true),

        Subject(name: "AP World History", code: "AP_WHIST", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade10],
                description: "College-level world history", isAP: true),

        Subject(name: "AP Psychology", code: "AP_PSYCH", category: .ap,
                schoolLevels: [.high], gradeLevels: [.grade11, .grade12],
                description: "Introduction to psychological science", isAP: true),

        // MARK: - World Languages

        Subject(name: "Spanish I", code: "SPAN1", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Beginning Spanish language"),

        Subject(name: "Spanish II", code: "SPAN2", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10],
                description: "Intermediate Spanish language"),

        Subject(name: "Spanish III", code: "SPAN3", category: .language,
                schoolLevels: [.high], gradeLevels: [.grade10, .grade11],
                description: "Advanced Spanish language"),

        Subject(name: "French I", code: "FREN1", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade6, .grade7, .grade8, .grade9],
                description: "Beginning French language"),

        Subject(name: "French II", code: "FREN2", category: .language,
                schoolLevels: [.middle, .high], gradeLevels: [.grade7, .grade8, .grade9, .grade10],
                description: "Intermediate French language"),

        // MARK: - Arts & Electives

        Subject(name: "Art", code: "ART", category: .arts,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Visual arts and creativity"),

        Subject(name: "Music", code: "MUSIC", category: .arts,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Music theory and performance"),

        Subject(name: "Physical Education", code: "PE", category: .physicalEducation,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Physical fitness and health"),

        // MARK: - Special Needs Support

        Subject(name: "Speech Therapy", code: "SPEECH", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Communication skills development"),

        Subject(name: "Occupational Therapy", code: "OT", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Daily living skills and motor development"),

        Subject(name: "Social Skills", code: "SOCIAL", category: .specialNeeds,
                schoolLevels: [.elementary, .middle, .high],
                gradeLevels: GradeLevel.allCases,
                description: "Social interaction and communication"),

        Subject(name: "Life Skills", code: "LIFESKILLS", category: .lifeSkills,
                schoolLevels: [.middle, .high],
                gradeLevels: [.grade6, .grade7, .grade8, .grade9, .grade10, .grade11, .grade12],
                description: "Independent living and practical skills")
    ]
}
