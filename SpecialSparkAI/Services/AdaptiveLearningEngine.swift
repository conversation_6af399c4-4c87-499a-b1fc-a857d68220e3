//
//  AdaptiveLearningEngine.swift
//  SpecialSparkAI
//
//  Phase 2: Adaptive Learning Engine Implementation
//

import Foundation
import SwiftData

// MARK: - Learning Assessment Models
@Model
class LearningAssessment: Identifiable, Codable {
    @Attribute(.unique) var id: UUID
    var studentId: UUID
    var subject: String
    var topic: String
    var questions: [AssessmentQuestion]
    var responses: [StudentResponse]
    var overallScore: Double
    var difficultyLevel: DifficultyLevel
    var learningStyle: LearningStyle
    var completedAt: Date?
    var timeSpent: TimeInterval
    var adaptiveRecommendations: [String]

    init(studentId: UUID, subject: String, topic: String, difficultyLevel: DifficultyLevel = .beginner) {
        self.id = UUID()
        self.studentId = studentId
        self.subject = subject
        self.topic = topic
        self.questions = []
        self.responses = []
        self.overallScore = 0.0
        self.difficultyLevel = difficultyLevel
        self.learningStyle = .visual
        self.completedAt = nil
        self.timeSpent = 0
        self.adaptiveRecommendations = []
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, studentId, subject, topic, questions, responses
        case overallScore, difficultyLevel, learningStyle, completedAt
        case timeSpent, adaptiveRecommendations
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(UUID.self, forKey: .id)
        self.studentId = try container.decode(UUID.self, forKey: .studentId)
        self.subject = try container.decode(String.self, forKey: .subject)
        self.topic = try container.decode(String.self, forKey: .topic)
        self.questions = try container.decode([AssessmentQuestion].self, forKey: .questions)
        self.responses = try container.decode([StudentResponse].self, forKey: .responses)
        self.overallScore = try container.decode(Double.self, forKey: .overallScore)
        self.difficultyLevel = try container.decode(DifficultyLevel.self, forKey: .difficultyLevel)
        self.learningStyle = try container.decode(LearningStyle.self, forKey: .learningStyle)
        self.completedAt = try container.decodeIfPresent(Date.self, forKey: .completedAt)
        self.timeSpent = try container.decode(TimeInterval.self, forKey: .timeSpent)
        self.adaptiveRecommendations = try container.decode([String].self, forKey: .adaptiveRecommendations)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(studentId, forKey: .studentId)
        try container.encode(subject, forKey: .subject)
        try container.encode(topic, forKey: .topic)
        try container.encode(questions, forKey: .questions)
        try container.encode(responses, forKey: .responses)
        try container.encode(overallScore, forKey: .overallScore)
        try container.encode(difficultyLevel, forKey: .difficultyLevel)
        try container.encode(learningStyle, forKey: .learningStyle)
        try container.encode(completedAt, forKey: .completedAt)
        try container.encode(timeSpent, forKey: .timeSpent)
        try container.encode(adaptiveRecommendations, forKey: .adaptiveRecommendations)
    }
}

// Simple Assessment Question for Adaptive Learning
struct SimpleAssessmentQuestion: Identifiable, Codable {
    let id = UUID()
    let question: String
    let options: [String]
    let correctAnswer: String
    let explanation: String
    let difficulty: DifficultyLevel
    let learningObjective: String
    let timeLimit: TimeInterval?

    init(question: String, options: [String], correctAnswer: String, explanation: String, difficulty: DifficultyLevel, learningObjective: String, timeLimit: TimeInterval? = nil) {
        self.question = question
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.difficulty = difficulty
        self.learningObjective = learningObjective
        self.timeLimit = timeLimit
    }
}

struct StudentResponse: Identifiable, Codable {
    let id = UUID()
    let questionId: UUID
    let selectedAnswer: String
    let isCorrect: Bool
    let timeSpent: TimeInterval
    let confidence: ConfidenceLevel
    let timestamp: Date

    init(questionId: UUID, selectedAnswer: String, isCorrect: Bool, timeSpent: TimeInterval, confidence: ConfidenceLevel = .medium) {
        self.questionId = questionId
        self.selectedAnswer = selectedAnswer
        self.isCorrect = isCorrect
        self.timeSpent = timeSpent
        self.confidence = confidence
        self.timestamp = Date()
    }
}

// MARK: - Learning Enums
enum DifficultyLevel: String, Codable, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"

    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        case .expert: return "Expert"
        }
    }

    var scoreMultiplier: Double {
        switch self {
        case .beginner: return 1.0
        case .intermediate: return 1.2
        case .advanced: return 1.5
        case .expert: return 2.0
        }
    }
}

enum LearningStyle: String, Codable, CaseIterable {
    case visual = "visual"
    case auditory = "auditory"
    case kinesthetic = "kinesthetic"
    case readingWriting = "reading_writing"

    var displayName: String {
        switch self {
        case .visual: return "Visual"
        case .auditory: return "Auditory"
        case .kinesthetic: return "Kinesthetic"
        case .readingWriting: return "Reading/Writing"
        }
    }
}

enum ConfidenceLevel: String, Codable, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"

    var displayName: String {
        switch self {
        case .low: return "Not Sure"
        case .medium: return "Somewhat Sure"
        case .high: return "Very Sure"
        }
    }
}

// MARK: - Adaptive Learning Engine
class AdaptiveLearningEngine: ObservableObject {
    static let shared = AdaptiveLearningEngine()

    @Published var currentAssessment: LearningAssessment?
    @Published var isAssessing = false
    @Published var learningProgress: [String: Double] = [:]

    private let geminiService = GeminiService.shared
    private let supabaseService = SupabaseService.shared

    private init() {}

    // MARK: - Assessment Creation
    func createAssessment(
        for student: Student,
        subject: String,
        topic: String,
        difficulty: DifficultyLevel = .beginner
    ) async -> LearningAssessment {

        let assessment = LearningAssessment(
            studentId: student.id,
            subject: subject,
            topic: topic,
            difficultyLevel: difficulty
        )

        // Generate questions using AI
        let questions = await generateQuestions(
            subject: subject,
            topic: topic,
            difficulty: difficulty,
            gradeLevel: student.gradeLevel,
            specialNeeds: student.specialNeeds
        )

        assessment.questions = questions

        await MainActor.run {
            self.currentAssessment = assessment
        }

        return assessment
    }

    // MARK: - Question Generation
    private func generateQuestions(
        subject: String,
        topic: String,
        difficulty: DifficultyLevel,
        gradeLevel: GradeLevel,
        specialNeeds: [SpecialNeed]
    ) async -> [AssessmentQuestion] {

        let prompt = """
        Generate 5 assessment questions for:
        - Subject: \(subject)
        - Topic: \(topic)
        - Difficulty: \(difficulty.displayName)
        - Grade Level: \(gradeLevel.displayName)
        - Special Needs: \(specialNeeds.map { $0.displayName }.joined(separator: ", "))

        For each question, provide:
        1. Clear, age-appropriate question text
        2. 4 multiple choice options (A, B, C, D)
        3. Correct answer
        4. Simple explanation
        5. Learning objective

        Make questions engaging and appropriate for special needs learners.
        Use simple language and clear concepts.
        """

        do {
            let response = try await geminiService.generateResponse(
                prompt: prompt,
                context: "Assessment question generation",
                personality: "Educational Assessment Specialist"
            )

            return parseQuestionsFromResponse(response, difficulty: difficulty)

        } catch {
            print("Failed to generate questions: \(error)")
            return createFallbackQuestions(subject: subject, topic: topic, difficulty: difficulty)
        }
    }

    private func parseQuestionsFromResponse(_ response: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        // Simple parsing - in a real app, use more sophisticated parsing
        let lines = response.components(separatedBy: .newlines)
        var questions: [AssessmentQuestion] = []

        // For now, create sample questions based on the response
        for i in 1...3 {
            let question = AssessmentQuestion(
                question: "Sample question \(i) from AI response",
                options: ["Option A", "Option B", "Option C", "Option D"],
                correctAnswer: "Option A",
                explanation: "This is the correct answer because...",
                difficulty: difficulty,
                learningObjective: "Understand basic concepts"
            )
            questions.append(question)
        }

        return questions
    }

    private func createFallbackQuestions(subject: String, topic: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        switch subject.lowercased() {
        case "mathematics", "math":
            return createMathQuestions(topic: topic, difficulty: difficulty)
        case "science":
            return createScienceQuestions(topic: topic, difficulty: difficulty)
        case "reading", "english":
            return createReadingQuestions(topic: topic, difficulty: difficulty)
        default:
            return createGeneralQuestions(subject: subject, topic: topic, difficulty: difficulty)
        }
    }

    private func createMathQuestions(topic: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        return [
            AssessmentQuestion(
                id: UUID(),
                questionText: "What is 2 + 3?",
                questionType: .multipleChoice,
                options: [
                    QuestionOption(id: UUID(), text: "4", isCorrect: false, feedback: nil, multimedia: nil),
                    QuestionOption(id: UUID(), text: "5", isCorrect: true, feedback: "Correct! 2 + 3 = 5", multimedia: nil),
                    QuestionOption(id: UUID(), text: "6", isCorrect: false, feedback: nil, multimedia: nil),
                    QuestionOption(id: UUID(), text: "7", isCorrect: false, feedback: nil, multimedia: nil)
                ],
                correctAnswers: ["5"],
                points: 1,
                difficulty: .easy,
                bloomsLevel: .knowledge,
                learningObjective: "Basic addition skills",
                hints: [],
                multimedia: [],
                adaptiveRules: [],
                timeLimit: nil,
                allowPartialCredit: false
            )
        ]
    }

    private func createScienceQuestions(topic: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        return [
            AssessmentQuestion(
                question: "What do plants need to grow?",
                options: ["Only water", "Only sunlight", "Water and sunlight", "Nothing"],
                correctAnswer: "Water and sunlight",
                explanation: "Plants need both water and sunlight to grow healthy and strong.",
                difficulty: difficulty,
                learningObjective: "Plant growth requirements"
            ),
            AssessmentQuestion(
                question: "What color do you get when you mix red and yellow?",
                options: ["Blue", "Green", "Orange", "Purple"],
                correctAnswer: "Orange",
                explanation: "When you mix red and yellow, you get orange!",
                difficulty: difficulty,
                learningObjective: "Color mixing"
            )
        ]
    }

    private func createReadingQuestions(topic: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        return [
            AssessmentQuestion(
                question: "What sound does the letter 'B' make?",
                options: ["/b/ like in 'ball'", "/d/ like in 'dog'", "/p/ like in 'pig'", "/m/ like in 'mom'"],
                correctAnswer: "/b/ like in 'ball'",
                explanation: "The letter B makes the /b/ sound, like at the beginning of 'ball'.",
                difficulty: difficulty,
                learningObjective: "Letter sound recognition"
            )
        ]
    }

    private func createGeneralQuestions(subject: String, topic: String, difficulty: DifficultyLevel) -> [AssessmentQuestion] {
        return [
            AssessmentQuestion(
                question: "This is a sample question about \(topic)",
                options: ["Option 1", "Option 2", "Option 3", "Option 4"],
                correctAnswer: "Option 1",
                explanation: "This is a sample explanation.",
                difficulty: difficulty,
                learningObjective: "General understanding"
            )
        ]
    }

    // MARK: - Assessment Processing
    func submitResponse(
        _ response: StudentResponse,
        to assessment: LearningAssessment
    ) async {
        assessment.responses.append(response)

        // Calculate current score
        let correctResponses = assessment.responses.filter { $0.isCorrect }.count
        assessment.overallScore = Double(correctResponses) / Double(assessment.responses.count) * 100

        // Check if assessment is complete
        if assessment.responses.count >= assessment.questions.count {
            await completeAssessment(assessment)
        }

        await MainActor.run {
            self.currentAssessment = assessment
        }
    }

    private func completeAssessment(_ assessment: LearningAssessment) async {
        assessment.completedAt = Date()

        // Generate adaptive recommendations
        let recommendations = await generateAdaptiveRecommendations(assessment)
        assessment.adaptiveRecommendations = recommendations

        // Update learning progress
        await updateLearningProgress(assessment)

        // Save to database
        if !APIConfig.Development.useMockData {
            await saveAssessment(assessment)
        }

        await MainActor.run {
            self.isAssessing = false
        }
    }

    private func generateAdaptiveRecommendations(_ assessment: LearningAssessment) async -> [String] {
        let prompt = """
        Based on this assessment result:
        - Subject: \(assessment.subject)
        - Topic: \(assessment.topic)
        - Score: \(assessment.overallScore)%
        - Difficulty: \(assessment.difficultyLevel.displayName)
        - Time spent: \(Int(assessment.timeSpent)) seconds

        Generate 3-5 specific learning recommendations for this student.
        Focus on areas for improvement and next steps.
        Keep recommendations encouraging and actionable.
        """

        do {
            let response = try await geminiService.generateResponse(
                prompt: prompt,
                context: "Adaptive learning recommendations",
                personality: "Learning Coach"
            )

            return parseRecommendations(response)

        } catch {
            return generateFallbackRecommendations(assessment)
        }
    }

    private func parseRecommendations(_ response: String) -> [String] {
        // Simple parsing - split by lines and filter
        return response.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty && $0.count > 10 }
            .prefix(5)
            .map { String($0) }
    }

    private func generateFallbackRecommendations(_ assessment: LearningAssessment) -> [String] {
        var recommendations: [String] = []

        if assessment.overallScore >= 80 {
            recommendations.append("Great job! You're ready for more challenging topics.")
            recommendations.append("Try exploring advanced concepts in \(assessment.subject).")
        } else if assessment.overallScore >= 60 {
            recommendations.append("Good progress! Let's practice a bit more.")
            recommendations.append("Review the concepts you found challenging.")
        } else {
            recommendations.append("Let's take it step by step and practice more.")
            recommendations.append("Don't worry - learning takes time and practice!")
        }

        recommendations.append("Keep up the great work!")

        return recommendations
    }

    private func updateLearningProgress(_ assessment: LearningAssessment) async {
        let key = "\(assessment.subject)_\(assessment.topic)"

        await MainActor.run {
            self.learningProgress[key] = assessment.overallScore
        }
    }

    private func saveAssessment(_ assessment: LearningAssessment) async {
        do {
            try await supabaseService.saveAssessment(assessment)
        } catch {
            print("Failed to save assessment: \(error)")
        }
    }

    // MARK: - Learning Style Detection
    func detectLearningStyle(from responses: [StudentResponse]) -> LearningStyle {
        // Simple learning style detection based on response patterns
        let averageTime = responses.map { $0.timeSpent }.reduce(0, +) / Double(responses.count)

        if averageTime < 10 {
            return .visual // Quick responses suggest visual processing
        } else if averageTime > 30 {
            return .readingWriting // Longer responses suggest careful reading
        } else {
            return .kinesthetic // Medium time suggests hands-on approach
        }
    }

    // MARK: - Difficulty Adjustment
    func adjustDifficulty(based assessment: LearningAssessment) -> DifficultyLevel {
        let score = assessment.overallScore

        switch assessment.difficultyLevel {
        case .beginner:
            return score >= 80 ? .intermediate : .beginner
        case .intermediate:
            if score >= 85 {
                return .advanced
            } else if score < 60 {
                return .beginner
            } else {
                return .intermediate
            }
        case .advanced:
            if score >= 90 {
                return .expert
            } else if score < 70 {
                return .intermediate
            } else {
                return .advanced
            }
        case .expert:
            return score < 75 ? .advanced : .expert
        }
    }
}
