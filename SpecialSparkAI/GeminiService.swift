//
//  GeminiService.swift
//  SpecialSparkAI
//
//  Gemini Flash 2.0 Integration for AI Agent Intelligence
//

import Foundation

class GeminiService: ObservableObject {
    static let shared = GeminiService()

    @Published var isInitialized = false
    @Published var isProcessing = false
    @Published var lastError: String?

    private let apiKey = "YOUR_GEMINI_API_KEY" // Replace with your Gemini API key
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta"
    private var session: URLSession

    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60
        config.timeoutIntervalForResource = 120
        self.session = URLSession(configuration: config)
    }

    // MARK: - Initialization

    func initialize() async {
        do {
            let isValid = await validateAPIKey()
            await MainActor.run {
                self.isInitialized = isValid
                if !isValid {
                    self.lastError = "Invalid Gemini API key"
                }
            }
        }
    }

    private func validateAPIKey() async -> Bool {
        let testPrompt = "Hello, this is a test."
        let response = await generateResponse(prompt: testPrompt, context: "", personality: nil)
        return !response.isEmpty && response != "Error generating response"
    }

    // MARK: - Core AI Generation

    func generateResponse(
        prompt: String,
        context: String,
        personality: AgentPersonality?
    ) async -> String {
        await MainActor.run { self.isProcessing = true }
        defer { Task { await MainActor.run { self.isProcessing = false } } }

        let systemPrompt = buildSystemPrompt(personality: personality)
        let fullPrompt = buildFullPrompt(system: systemPrompt, context: context, prompt: prompt)

        let response = await callGeminiAPI(prompt: fullPrompt)
        return response
    }

    func generatePersonalizedResponse(
        for student: Student,
        agent: AIAgent,
        prompt: String,
        context: String
    ) async -> String {
        let personalizedPrompt = buildPersonalizedPrompt(
            student: student,
            agent: agent,
            prompt: prompt,
            context: context
        )

        return await generateResponse(
            prompt: personalizedPrompt,
            context: context,
            personality: agent.personality
        )
    }

    // MARK: - Specialized AI Functions

    func analyzeInteractionPatterns(_ interactions: [LearningInteraction]) async -> InteractionAnalysis {
        let analysisPrompt = buildInteractionAnalysisPrompt(interactions)
        let response = await generateResponse(prompt: analysisPrompt, context: "", personality: nil)

        return parseInteractionAnalysis(response)
    }

    func generateCapabilities(
        for agentType: AgentType,
        specialization: String
    ) async -> [AgentCapability] {
        let capabilityPrompt = buildCapabilityGenerationPrompt(agentType: agentType, specialization: specialization)
        let response = await generateResponse(prompt: capabilityPrompt, context: "", personality: nil)

        return parseCapabilities(response, agentType: agentType)
    }

    func generateLearningObjectives(
        for student: Student,
        subject: Subject,
        currentLevel: String
    ) async -> [String] {
        let objectivePrompt = buildObjectiveGenerationPrompt(
            student: student,
            subject: subject,
            currentLevel: currentLevel
        )
        let response = await generateResponse(prompt: objectivePrompt, context: "", personality: nil)

        return parseObjectives(response)
    }

    func assessStudentUnderstanding(
        studentResponse: String,
        expectedAnswer: String,
        context: String
    ) async -> AssessmentResult {
        let assessmentPrompt = buildAssessmentPrompt(
            studentResponse: studentResponse,
            expectedAnswer: expectedAnswer,
            context: context
        )
        let response = await generateResponse(prompt: assessmentPrompt, context: "", personality: nil)

        return parseAssessmentResult(response)
    }

    func generateAdaptiveContent(
        for student: Student,
        topic: String,
        difficulty: String,
        learningStyle: String
    ) async -> AdaptiveContent {
        let contentPrompt = buildAdaptiveContentPrompt(
            student: student,
            topic: topic,
            difficulty: difficulty,
            learningStyle: learningStyle
        )
        let response = await generateResponse(prompt: contentPrompt, context: "", personality: nil)

        return parseAdaptiveContent(response)
    }

    // MARK: - Emotional Intelligence

    func analyzeEmotionalState(
        studentInput: String,
        context: String
    ) async -> EmotionalState {
        let emotionPrompt = buildEmotionAnalysisPrompt(input: studentInput, context: context)
        let response = await generateResponse(prompt: emotionPrompt, context: "", personality: nil)

        return parseEmotionalState(response)
    }

    func generateEmotionalSupport(
        for emotionalState: EmotionalState,
        student: Student,
        context: String
    ) async -> String {
        let supportPrompt = buildEmotionalSupportPrompt(
            emotionalState: emotionalState,
            student: student,
            context: context
        )

        return await generateResponse(prompt: supportPrompt, context: context, personality: nil)
    }

    // MARK: - Private Helper Methods

    private func callGeminiAPI(prompt: String) async -> String {
        guard let url = URL(string: "\(baseURL)/models/gemini-2.0-flash-exp:generateContent?key=\(apiKey)") else {
            return "Error: Invalid API URL"
        }

        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ],
            generationConfig: GeminiGenerationConfig(
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048
            ),
            safetySettings: [
                GeminiSafetySettings(
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                ),
                GeminiSafetySettings(
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                )
            ]
        )

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let jsonData = try JSONEncoder().encode(requestBody)
            request.httpBody = jsonData

            let (data, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                return "Error: HTTP \(httpResponse.statusCode)"
            }

            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
            return geminiResponse.candidates.first?.content.parts.first?.text ?? "No response generated"

        } catch {
            return "Error generating response: \(error.localizedDescription)"
        }
    }

    private func buildSystemPrompt(personality: AgentPersonality?) -> String {
        guard let personality = personality else {
            return "You are a helpful AI teacher assistant."
        }

        var prompt = "You are an AI teacher with the following personality traits:\n"
        prompt += "- Warmth level: \(personality.warmth)/10\n"
        prompt += "- Patience level: \(personality.patience)/10\n"
        prompt += "- Enthusiasm level: \(personality.enthusiasm)/10\n"
        prompt += "- Humor level: \(personality.humor)/10\n"
        prompt += "- Empathy level: \(personality.empathy)/10\n"
        prompt += "- Communication style: \(personality.communicationStyle.rawValue)\n"
        prompt += "- Preferred interaction mode: \(personality.preferredInteractionMode.rawValue)\n"

        if !personality.specialNeedsAdaptations.isEmpty {
            prompt += "- Special needs adaptations: \(personality.specialNeedsAdaptations.joined(separator: ", "))\n"
        }

        prompt += "\nAlways respond in character with these personality traits. Be supportive, encouraging, and adaptive to the student's needs."

        return prompt
    }

    private func buildFullPrompt(system: String, context: String, prompt: String) -> String {
        var fullPrompt = system + "\n\n"

        if !context.isEmpty {
            fullPrompt += "Context: \(context)\n\n"
        }

        fullPrompt += "Student input: \(prompt)\n\n"
        fullPrompt += "Please respond as the AI teacher described above:"

        return fullPrompt
    }

    private func buildPersonalizedPrompt(
        student: Student,
        agent: AIAgent,
        prompt: String,
        context: String
    ) -> String {
        var personalizedPrompt = "Student Profile:\n"
        personalizedPrompt += "- Name: \(student.firstName)\n"
        personalizedPrompt += "- Grade level: \(student.gradeLevel)\n"
        personalizedPrompt += "- Special needs: \(student.specialNeeds.map { $0.rawValue }.joined(separator: ", "))\n"
        personalizedPrompt += "- Academic level: \(student.academicLevel.rawValue)\n\n"

        personalizedPrompt += "Agent Role: \(agent.role)\n"
        personalizedPrompt += "Specialization: \(agent.specialization)\n\n"

        personalizedPrompt += "Current context: \(context)\n\n"
        personalizedPrompt += "Student says: \(prompt)\n\n"
        personalizedPrompt += "Respond in a way that's perfectly tailored to this specific student's needs, learning style, and current emotional state."

        return personalizedPrompt
    }

    private func buildInteractionAnalysisPrompt(_ interactions: [LearningInteraction]) -> String {
        var prompt = "Analyze the following learning interactions and provide insights:\n\n"

        for interaction in interactions.prefix(10) { // Analyze last 10 interactions
            prompt += "Interaction: \(interaction.interactionType.rawValue)\n"
            prompt += "Duration: \(interaction.duration) seconds\n"
            prompt += "Emotional state: \(interaction.emotionalState.rawValue)\n"
            prompt += "Outcome: \(interaction.outcome.rawValue)\n"
            prompt += "Content snippet: \(String(interaction.content.prefix(100)))...\n\n"
        }

        prompt += """
        Please analyze these interactions and provide:
        1. Whether the agent needs more patience (true/false)
        2. Whether the agent needs more enthusiasm (true/false)
        3. Whether a different communication style is needed (true/false)
        4. Recommended communication style if change is needed
        5. Recommended adaptations for better learning
        6. Overall effectiveness score (0.0 to 1.0)

        Format your response as JSON with these exact keys:
        {
            "needsMorePatience": boolean,
            "needsMoreEnthusiasm": boolean,
            "needsDifferentCommunicationStyle": boolean,
            "recommendedStyle": "style_name",
            "recommendedAdaptations": ["adaptation1", "adaptation2"],
            "overallEffectiveness": 0.85
        }
        """

        return prompt
    }

    // Additional parsing methods would go here...
    private func parseInteractionAnalysis(_ response: String) -> InteractionAnalysis {
        // Parse JSON response into InteractionAnalysis struct
        // Implementation would parse the JSON and create the struct
        return InteractionAnalysis(
            needsMorePatience: false,
            needsMoreEnthusiasm: false,
            needsDifferentCommunicationStyle: false,
            recommendedStyle: AgentCommunicationStyle.adaptive,
            recommendedAdaptations: [],
            overallEffectiveness: 0.8
        )
    }

    private func parseCapabilities(_ response: String, agentType: AgentType) -> [AgentCapability] {
        // Parse response and create AgentCapability objects
        return []
    }

    private func parseObjectives(_ response: String) -> [String] {
        // Parse response and extract learning objectives
        return []
    }

    private func parseAssessmentResult(_ response: String) -> AssessmentResult {
        // Parse response and create AssessmentResult
        return AssessmentResult(score: 0.8, feedback: "Good work!", nextSteps: [])
    }

    private func parseAdaptiveContent(_ response: String) -> AdaptiveContent {
        // Parse response and create AdaptiveContent
        return AdaptiveContent(content: "", activities: [], assessments: [])
    }

    private func parseEmotionalState(_ response: String) -> EmotionalState {
        // Parse response and determine emotional state
        return .neutral
    }

    // Additional prompt building methods...
    private func buildCapabilityGenerationPrompt(agentType: AgentType, specialization: String) -> String {
        return "Generate capabilities for \(agentType.rawValue) specializing in \(specialization)"
    }

    private func buildObjectiveGenerationPrompt(student: Student, subject: Subject, currentLevel: String) -> String {
        return "Generate learning objectives for \(student.firstName) in \(subject.rawValue) at \(currentLevel) level"
    }

    private func buildAssessmentPrompt(studentResponse: String, expectedAnswer: String, context: String) -> String {
        return "Assess student response: '\(studentResponse)' against expected: '\(expectedAnswer)' in context: '\(context)'"
    }

    private func buildAdaptiveContentPrompt(student: Student, topic: String, difficulty: String, learningStyle: String) -> String {
        return "Create adaptive content for \(student.firstName) on \(topic) at \(difficulty) difficulty for \(learningStyle) learner"
    }

    private func buildEmotionAnalysisPrompt(input: String, context: String) -> String {
        return "Analyze emotional state from: '\(input)' in context: '\(context)'"
    }

    private func buildEmotionalSupportPrompt(emotionalState: EmotionalState, student: Student, context: String) -> String {
        return "Provide emotional support for \(student.firstName) who is feeling \(emotionalState.rawValue) in context: '\(context)'"
    }
}

// MARK: - Gemini API Models

struct GeminiRequest: Codable {
    let contents: [GeminiContent]
    let generationConfig: GeminiGenerationConfig
    let safetySettings: [GeminiSafetySettings]
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String
}

struct GeminiGenerationConfig: Codable {
    let temperature: Double
    let topK: Int
    let topP: Double
    let maxOutputTokens: Int
}

struct GeminiSafetySettings: Codable {
    let category: String
    let threshold: String
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]
}

struct GeminiCandidate: Codable {
    let content: GeminiContent
}

// MARK: - Supporting Types

struct AssessmentResult {
    let score: Double
    let feedback: String
    let nextSteps: [String]
}

struct AdaptiveContent {
    let content: String
    let activities: [String]
    let assessments: [String]
}
