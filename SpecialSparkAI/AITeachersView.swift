//
//  AITeachersView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct AITeachersView: View {
    let student: Student?
    @Query private var teachers: [AITeacher]
    @State private var selectedTeacher: AITeacher?
    @State private var showingTeacherDetail = false
    @State private var searchText = ""
    @State private var selectedSubjectFilter: Subject?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and filter bar
                searchAndFilterView

                ScrollView {
                    VStack(spacing: 20) {
                        // Featured teacher
                        featuredTeacherView

                        // Available teachers
                        availableTeachersView

                        // Teacher specializations
                        specializationsView
                    }
                    .padding()
                }
            }
            .navigationTitle("AI Teachers")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingTeacherDetail) {
            if let teacher = selectedTeacher {
                AITeacherDetailView(teacher: teacher, student: student)
            }
        }
    }

    private var searchAndFilterView: some View {
        VStack(spacing: 10) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search teachers...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
            .padding(.horizontal)

            // Subject filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    SubjectFilterChip(
                        title: "All",
                        isSelected: selectedSubjectFilter == nil
                    ) {
                        selectedSubjectFilter = nil
                    }

                    ForEach(Subject.allCases, id: \.self) { subject in
                        SubjectFilterChip(
                            title: subject.rawValue,
                            isSelected: selectedSubjectFilter == subject
                        ) {
                            selectedSubjectFilter = subject
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical, 10)
        .background(.ultraThinMaterial)
    }

    private var featuredTeacherView: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Featured Teacher")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
                    .font(.caption)
            }

            if let featuredTeacher = getFeaturedTeacher() {
                FeaturedTeacherCard(teacher: featuredTeacher) {
                    selectedTeacher = featuredTeacher
                    showingTeacherDetail = true
                }
            }
        }
    }

    private var availableTeachersView: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Meet Your Teachers")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(filteredTeachers.count) available")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                ForEach(filteredTeachers) { teacher in
                    AITeacherCard(
                        teacher: teacher,
                        isAvailable: isTeacherAvailable(teacher: teacher)
                    ) {
                        selectedTeacher = teacher
                        showingTeacherDetail = true
                    }
                }
            }
        }
    }

    private var specializationsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Teaching Specializations")
                .font(.headline)
                .fontWeight(.semibold)

            let allSpecializations = getAllSpecializations()

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 10) {
                ForEach(allSpecializations, id: \.self) { specialization in
                    SpecializationChip(
                        title: specialization,
                        count: getTeacherCount(for: specialization)
                    ) {
                        // Filter by specialization
                        searchText = specialization
                    }
                }
            }
        }
    }

    // Helper computed properties and methods
    private var filteredTeachers: [AITeacher] {
        var filtered = teachers

        // Filter by subject
        if let selectedSubject = selectedSubjectFilter {
            filtered = filtered.filter { $0.subject == selectedSubject }
        }

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { teacher in
                teacher.name.localizedCaseInsensitiveContains(searchText) ||
                teacher.subject.rawValue.localizedCaseInsensitiveContains(searchText) ||
                teacher.specializations.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }

        return filtered
    }

    private func getFeaturedTeacher() -> AITeacher? {
        // In a real app, this would be based on student's needs or current curriculum
        return teachers.first
    }

    private func isTeacherAvailable(teacher: AITeacher) -> Bool {
        // In a real app, this would check the teacher's current status
        return Bool.random() // Mock availability
    }

    private func getAllSpecializations() -> [String] {
        let allSpecs = teachers.flatMap { $0.specializations }
        return Array(Set(allSpecs)).sorted()
    }

    private func getTeacherCount(for specialization: String) -> Int {
        return teachers.filter { $0.specializations.contains(specialization) }.count
    }
}

struct SubjectFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(isSelected ? .blue : .clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 15)
                                .stroke(.blue, lineWidth: 1)
                        )
                )
                .foregroundColor(isSelected ? .white : .blue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FeaturedTeacherCard: View {
    let teacher: AITeacher
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 20) {
                // Teacher avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: subjectGradient(for: teacher.subject),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 100, height: 100)

                    Text(String(teacher.name.prefix(1)))
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    // Featured badge
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .background(
                            Circle()
                                .fill(.white)
                                .frame(width: 24, height: 24)
                        )
                        .offset(x: 35, y: -35)
                }

                // Teacher info
                VStack(alignment: .leading, spacing: 8) {
                    Text(teacher.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(teacher.subject.rawValue)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("Specializes in personalized learning and special needs support")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)

                    HStack {
                        Label("Available Now", systemImage: "circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)

                        Spacer()

                        Text("Chat Now")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                }

                Spacer()
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.yellow.opacity(0.3), lineWidth: 2)
                    )
                    .shadow(radius: 5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func subjectGradient(for subject: Subject) -> [Color] {
        switch subject {
        case .mathematics:
            return [.blue, .cyan]
        case .english:
            return [.purple, .pink]
        case .science:
            return [.green, .mint]
        case .socialStudies:
            return [.orange, .yellow]
        case .art:
            return [.pink, .red]
        case .music:
            return [.indigo, .purple]
        case .physicalEducation:
            return [.red, .orange]
        case .computerScience:
            return [.teal, .blue]
        case .foreignLanguage:
            return [.mint, .green]
        case .specialEducation:
            return [.purple, .blue]
        case .counseling:
            return [.pink, .purple]
        case .libraryScience:
            return [.brown, .orange]
        }
    }
}

struct SpecializationChip: View {
    let title: String
    let count: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 5) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)

                Text("\(count)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .padding(.horizontal, 4)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AITeacherDetailView: View {
    let teacher: AITeacher
    let student: Student?
    @State private var showingChat = false
    @State private var showingSchedule = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 25) {
                    // Teacher header
                    teacherHeaderView

                    // Personality traits
                    personalityTraitsView

                    // Specializations
                    specializationsDetailView

                    // Teaching approach
                    teachingApproachView

                    // Student testimonials (mock)
                    testimonialsView

                    // Action buttons
                    actionButtonsView
                }
                .padding()
            }
            .navigationTitle(teacher.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        // Dismiss view
                    }
                }
            }
        }
        .sheet(isPresented: $showingChat) {
            TeacherChatView(teacher: teacher, student: student)
        }
        .sheet(isPresented: $showingSchedule) {
            ScheduleLessonView(teacher: teacher, student: student)
        }
    }

    private var teacherHeaderView: some View {
        VStack(spacing: 20) {
            // Avatar
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: subjectGradient(for: teacher.subject),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)

                Text(String(teacher.name.prefix(1)))
                    .font(.system(size: 48))
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                // Status indicator
                Circle()
                    .fill(.green)
                    .frame(width: 24, height: 24)
                    .overlay(
                        Circle()
                            .stroke(.white, lineWidth: 3)
                    )
                    .offset(x: 40, y: 40)
            }

            // Basic info
            VStack(spacing: 8) {
                Text(teacher.name)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(teacher.subject.rawValue + " Teacher")
                    .font(.title3)
                    .foregroundColor(.secondary)

                Text(teacher.experienceLevel.rawValue + " Level")
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.blue.opacity(0.1))
                    )
            }
        }
    }

    private var personalityTraitsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Personality Traits")
                .font(.headline)
                .fontWeight(.semibold)

            if let personality = teacher.personality {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    PersonalityTraitCard(title: "Warmth", value: personality.warmth, color: .pink)
                    PersonalityTraitCard(title: "Patience", value: personality.patience, color: .blue)
                    PersonalityTraitCard(title: "Enthusiasm", value: personality.enthusiasm, color: .orange)
                    PersonalityTraitCard(title: "Creativity", value: personality.creativity, color: .purple)
                    PersonalityTraitCard(title: "Empathy", value: personality.empathy, color: .green)
                    PersonalityTraitCard(title: "Adaptability", value: personality.adaptability, color: .teal)
                }
            }
        }
    }

    private var specializationsDetailView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Teaching Specializations")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 10) {
                ForEach(teacher.specializations, id: \.self) { specialization in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)

                        Text(specialization)
                            .font(.caption)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial)
                    )
                }
            }
        }
    }

    private var teachingApproachView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Teaching Approach")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                TeachingApproachCard(
                    title: "Personalized Learning",
                    description: "Adapts to each student's unique learning style and pace",
                    icon: "person.crop.circle.badge.checkmark"
                )

                TeachingApproachCard(
                    title: "Interactive Lessons",
                    description: "Engaging activities that make learning fun and memorable",
                    icon: "hands.sparkles"
                )

                TeachingApproachCard(
                    title: "Continuous Assessment",
                    description: "Regular feedback to track progress and adjust teaching",
                    icon: "chart.line.uptrend.xyaxis"
                )

                TeachingApproachCard(
                    title: "Emotional Support",
                    description: "Understanding and responding to students' emotional needs",
                    icon: "heart.circle"
                )
            }
        }
    }

    private var testimonialsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("What Students Say")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                TestimonialCard(
                    quote: "Ms. Sophia makes math so much fun! I never thought I could love numbers.",
                    student: "Emma, Grade 4",
                    rating: 5
                )

                TestimonialCard(
                    quote: "She's so patient and helps me understand everything step by step.",
                    student: "Alex, Grade 3",
                    rating: 5
                )
            }
        }
    }

    private var actionButtonsView: some View {
        VStack(spacing: 15) {
            // Primary action - Start chat
            Button(action: {
                showingChat = true
            }) {
                HStack {
                    Image(systemName: "message.circle.fill")
                        .font(.title3)
                    Text("Start Conversation")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(15)
            }

            // Secondary actions
            HStack(spacing: 15) {
                Button(action: {
                    showingSchedule = true
                }) {
                    HStack {
                        Image(systemName: "calendar.badge.plus")
                        Text("Schedule Lesson")
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.blue.opacity(0.1))
                    )
                }

                Button(action: {
                    // View teacher's lessons
                }) {
                    HStack {
                        Image(systemName: "book.circle")
                        Text("View Lessons")
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.green.opacity(0.1))
                    )
                }
            }
        }
    }

    private func subjectGradient(for subject: Subject) -> [Color] {
        switch subject {
        case .mathematics:
            return [.blue, .cyan]
        case .english:
            return [.purple, .pink]
        case .science:
            return [.green, .mint]
        case .socialStudies:
            return [.orange, .yellow]
        case .art:
            return [.pink, .red]
        case .music:
            return [.indigo, .purple]
        case .physicalEducation:
            return [.red, .orange]
        case .computerScience:
            return [.teal, .blue]
        case .foreignLanguage:
            return [.mint, .green]
        case .specialEducation:
            return [.purple, .blue]
        case .counseling:
            return [.pink, .purple]
        case .libraryScience:
            return [.brown, .orange]
        }
    }
}

// Supporting Views
struct PersonalityTraitCard: View {
    let title: String
    let value: Int
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)

            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 4)
                    .frame(width: 40, height: 40)

                Circle()
                    .trim(from: 0, to: Double(value) / 10.0)
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(-90))

                Text("\(value)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct TeachingApproachCard: View {
    let title: String
    let description: String
    let icon: String

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct TestimonialCard: View {
    let quote: String
    let student: String
    let rating: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("\"\(quote)\"")
                .font(.subheadline)
                .foregroundColor(.primary)
                .italic()

            HStack {
                HStack(spacing: 2) {
                    ForEach(0..<rating, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                    }
                }

                Spacer()

                Text("- \(student)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

// Placeholder views for sheets
struct TeacherChatView: View {
    let teacher: AITeacher
    let student: Student?

    var body: some View {
        NavigationView {
            VStack {
                Text("Chat with \(teacher.name)")
                    .font(.title)
                Text("AI-powered conversation interface would go here")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Chat")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct ScheduleLessonView: View {
    let teacher: AITeacher
    let student: Student?

    var body: some View {
        NavigationView {
            VStack {
                Text("Schedule with \(teacher.name)")
                    .font(.title)
                Text("Lesson scheduling interface would go here")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Schedule")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    AITeachersView(student: nil)
}
