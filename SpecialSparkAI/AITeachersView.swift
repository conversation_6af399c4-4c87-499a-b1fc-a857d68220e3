//
//  AITeachersView.swift
//  SpecialSparkAI
//
//  AI Agent Teachers with LangGraph and CrewAI Integration
//

import SwiftUI
import SwiftData

struct AITeachersView: View {
    let student: Student?
    @StateObject private var aiAgentService = AIAgentService()
    @State private var selectedAgent: AIAgent?
    @State private var showingAgentDetail = false
    @State private var searchText = ""
    @State private var selectedAgentType: AgentType?
    @State private var showingCreateAgent = false
    @State private var showingCrewBuilder = false
    @State private var isCreatingCrew = false
    @State private var selectedAgents: Set<UUID> = []

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // AI Agent Universe Header
                aiAgentUniverseHeader

                // Connection Status
                connectionStatusBar

                // Agent Type Filter
                agentTypeFilter

                // Search Bar
                searchBar

                // Main Content
                if aiAgentService.activeAgents.isEmpty {
                    emptyAgentState
                } else {
                    agentGridView
                }

                Spacer()

                // Action Buttons
                actionButtonsBar
            }
            .navigationTitle("AI Agent Universe")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Create New Agent", systemImage: "plus.circle") {
                            showingCreateAgent = true
                        }

                        Button("Build Agent Crew", systemImage: "person.3.fill") {
                            showingCrewBuilder = true
                        }

                        Button("Agent Analytics", systemImage: "chart.bar.fill") {
                            // Show analytics
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title2)
                    }
                }
            }
        }
        .sheet(isPresented: $showingCreateAgent) {
            CreateAgentView(aiAgentService: aiAgentService)
        }
        .sheet(isPresented: $showingCrewBuilder) {
            CrewBuilderView(
                aiAgentService: aiAgentService,
                availableAgents: aiAgentService.activeAgents
            )
        }
        .sheet(item: $selectedAgent) { agent in
            AgentDetailView(agent: agent, aiAgentService: aiAgentService)
        }
    }

    // MARK: - UI Components

    private var aiAgentUniverseHeader: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Agent Universe")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple, .pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Infinite AI teachers powered by LangGraph & CrewAI")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Agent Stats
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(aiAgentService.activeAgents.count)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("Active Agents")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Real-time Activity Indicator
            if aiAgentService.isProcessing {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)

                    Text("AI Agents are collaborating...")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }

    private var connectionStatusBar: some View {
        HStack {
            Circle()
                .fill(connectionStatusColor)
                .frame(width: 8, height: 8)

            Text(connectionStatusText)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            if case .connected = aiAgentService.connectionStatus {
                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.green)
                    Text("Gemini Flash 2.0")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }

    private var connectionStatusColor: Color {
        switch aiAgentService.connectionStatus {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected:
            return .red
        case .error:
            return .red
        }
    }

    private var connectionStatusText: String {
        switch aiAgentService.connectionStatus {
        case .connected:
            return "Connected to AI Services"
        case .connecting:
            return "Connecting to AI Services..."
        case .disconnected:
            return "Disconnected from AI Services"
        case .error(let message):
            return "Error: \(message)"
        }
    }

    private var agentTypeFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                AgentTypeFilterChip(
                    title: "All Agents",
                    isSelected: selectedAgentType == nil,
                    icon: "sparkles"
                ) {
                    selectedAgentType = nil
                }

                ForEach(AgentType.allCases, id: \.self) { agentType in
                    AgentTypeFilterChip(
                        title: agentType.rawValue,
                        isSelected: selectedAgentType == agentType,
                        icon: iconForAgentType(agentType)
                    ) {
                        selectedAgentType = agentType
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search AI agents...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button("Clear") {
                    searchText = ""
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    private var emptyAgentState: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "brain.head.profile")
                .font(.system(size: 80))
                .foregroundColor(.blue.opacity(0.3))

            VStack(spacing: 8) {
                Text("No AI Agents Yet")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Create your first AI agent to start building your virtual teaching team")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("Create First Agent") {
                showingCreateAgent = true
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)

            Spacer()
        }
        .padding()
    }

    private var agentGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(filteredAgents) { agent in
                    AIAgentCard(agent: agent) {
                        selectedAgent = agent
                        showingAgentDetail = true
                    }
                }
            }
            .padding()
        }
    }

    private var actionButtonsBar: some View {
        HStack(spacing: 16) {
            Button("Quick Chat") {
                // Start quick chat with available agent
                if let agent = aiAgentService.activeAgents.first {
                    selectedAgent = agent
                    showingAgentDetail = true
                }
            }
            .buttonStyle(.bordered)
            .disabled(aiAgentService.activeAgents.isEmpty)

            Button("Create Crew") {
                showingCrewBuilder = true
            }
            .buttonStyle(.bordered)
            .disabled(aiAgentService.activeAgents.count < 2)

            Button("New Agent") {
                showingCreateAgent = true
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(.ultraThinMaterial)
    }

    // MARK: - Helper Methods

    private var filteredAgents: [AIAgent] {
        var filtered = aiAgentService.activeAgents

        // Filter by agent type
        if let selectedType = selectedAgentType {
            filtered = filtered.filter { $0.agentType == selectedType }
        }

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { agent in
                agent.name.localizedCaseInsensitiveContains(searchText) ||
                agent.specialization.localizedCaseInsensitiveContains(searchText) ||
                agent.agentType.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }

        return filtered
    }

    private func iconForAgentType(_ agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist:
            return "graduationcap.fill"
        case .learningCoach:
            return "person.crop.circle.badge.checkmark"
        case .emotionalSupport:
            return "heart.fill"
        case .assessmentAgent:
            return "chart.bar.fill"
        case .parentCommunicator:
            return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor:
            return "brain.head.profile"
        case .creativeMentor:
            return "paintbrush.fill"
        case .socialSkillsCoach:
            return "person.3.fill"
        }
    }
}

// MARK: - Supporting Views

struct AgentTypeFilterChip: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? .blue : .clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.blue, lineWidth: 1)
                    )
            )
            .foregroundColor(isSelected ? .white : .blue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AIAgentCard: View {
    let agent: AIAgent
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Agent Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: agentTypeGradient(for: agent.agentType),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)

                    Image(systemName: agentTypeIcon(for: agent.agentType))
                        .font(.title2)
                        .foregroundColor(.white)

                    // Status indicator
                    if agent.isActive {
                        Circle()
                            .fill(.green)
                            .frame(width: 16, height: 16)
                            .overlay(
                                Circle()
                                    .stroke(.white, lineWidth: 2)
                            )
                            .offset(x: 20, y: 20)
                    }
                }

                // Agent Info
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .lineLimit(1)

                    Text(agent.agentType.rawValue)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)

                    Text(agent.specialization)
                        .font(.caption2)
                        .foregroundColor(.blue)
                        .lineLimit(1)
                }

                // Current State
                HStack(spacing: 4) {
                    Circle()
                        .fill(stateColor(for: agent.currentState))
                        .frame(width: 6, height: 6)

                    Text(agent.currentState.rawValue)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.blue.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func agentTypeGradient(for agentType: AgentType) -> [Color] {
        switch agentType {
        case .subjectSpecialist:
            return [.blue, .cyan]
        case .learningCoach:
            return [.green, .mint]
        case .emotionalSupport:
            return [.pink, .red]
        case .assessmentAgent:
            return [.purple, .blue]
        case .parentCommunicator:
            return [.orange, .yellow]
        case .adaptiveTutor:
            return [.teal, .blue]
        case .creativeMentor:
            return [.pink, .purple]
        case .socialSkillsCoach:
            return [.indigo, .purple]
        }
    }

    private func agentTypeIcon(for agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist:
            return "graduationcap.fill"
        case .learningCoach:
            return "person.crop.circle.badge.checkmark"
        case .emotionalSupport:
            return "heart.fill"
        case .assessmentAgent:
            return "chart.bar.fill"
        case .parentCommunicator:
            return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor:
            return "brain.head.profile"
        case .creativeMentor:
            return "paintbrush.fill"
        case .socialSkillsCoach:
            return "person.3.fill"
        }
    }

    private func stateColor(for state: AgentState) -> Color {
        switch state {
        case .idle:
            return .gray
        case .teaching:
            return .green
        case .assessing:
            return .blue
        case .planning:
            return .orange
        case .collaborating:
            return .purple
        case .reflecting:
            return .yellow
        case .adapting:
            return .teal
        case .communicating:
            return .pink
        }
    }
}

// MARK: - Placeholder Views (to be implemented)

struct CreateAgentView: View {
    let aiAgentService: AIAgentService

    var body: some View {
        Text("Create Agent View - To be implemented")
    }
}

struct CrewBuilderView: View {
    let aiAgentService: AIAgentService
    let availableAgents: [AIAgent]

    var body: some View {
        Text("Crew Builder View - To be implemented")
    }
}

struct AgentDetailView: View {
    let agent: AIAgent
    let aiAgentService: AIAgentService

    var body: some View {
        Text("Agent Detail View - To be implemented")
    }
}
