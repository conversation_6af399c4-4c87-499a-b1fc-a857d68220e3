//
//  SpecialSparkAIApp.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

@main
struct SpecialSparkAIApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            // Student Models
            Student.self,
            // Subject is now a struct, not a SwiftData model
            StudentTeacherAssignment.self,
            LearningSession.self,

            // AI Agent Models
            AIAgent.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .modelContainer(sharedModelContainer)
    }
}
