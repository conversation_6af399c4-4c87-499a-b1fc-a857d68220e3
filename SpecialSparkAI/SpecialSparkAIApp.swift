//
//  SpecialSparkAIApp.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

@main
struct SpecialSparkAIApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            // Student Models
            Student.self,
            LearningProfile.self,
            SpecialNeed.self,
            Achievement.self,

            // AI Teacher Models
            AITeacher.self,
            TeacherPersonality.self,
            VoiceProfile.self,
            TeachingMethod.self,
            Lesson.self,
            LessonActivity.self,
            Assessment.self,
            AssessmentQuestion.self,

            // Virtual School Models
            VirtualSchool.self,
            VirtualCampus.self,
            VirtualBuilding.self,
            Floor.self,
            VirtualRoom.self,
            Equipment.self,
            RoomLayout.self,
            RoomAmbiance.self,
            AccessibilityFeatures.self,
            OutdoorSpace.self,
            Hallway.self,
            CommonArea.self,
            Furniture.self,
            Locker.self,
            Artwork.self,
            BulletinBoard.self,
            BulletinItem.self,
            EmergencyExit.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .modelContainer(sharedModelContainer)
    }
}
